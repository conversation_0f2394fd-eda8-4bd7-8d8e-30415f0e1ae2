# RetailUI API Documentation

## Table of Contents

1. [Core API](#core-api)
2. [Module System](#module-system)
3. [Performance & Security](#performance--security)
4. [Utilities](#utilities)
5. [Events](#events)
6. [Examples](#examples)

## Core API

### RUI (Main Addon Object)

The main RetailUI addon object provides core functionality and module management.

#### Methods

##### `RUI:CreateUIFrame(width, height, frameName)`
Creates a standardized UI frame with RetailUI styling and editor mode support.

**Parameters:**
- `width` (number): Frame width in pixels
- `height` (number): Frame height in pixels  
- `frameName` (string): Unique identifier for the frame

**Returns:**
- `Frame`: The created frame object

**Example:**
```lua
local myFrame = RUI:CreateUIFrame(200, 100, "MyCustomFrame")
myFrame:SetPoint("CENTER", UIParent, "CENTER", 0, 0)
```

##### `RUI:SaveUIFramePosition(frame, widgetName)`
Saves the current position of a UI frame with security validation.

**Parameters:**
- `frame` (Frame): The frame to save position for
- `widgetName` (string): Widget identifier for storage

**Security:** Requires `saveSettings` permission and validates frame operations.

##### `RUI:SaveUIFrameScale(input, widgetName)`
Saves the scale of a UI frame with input validation and security checks.

**Parameters:**
- `input` (string|number): Scale value (0.1 - 5.0)
- `widgetName` (string): Widget identifier for storage

**Validation:** Automatically sanitizes input and validates scale range.

##### `RUI:GetUIFrameScale(widgetName)`
Retrieves the saved scale for a widget.

**Parameters:**
- `widgetName` (string): Widget identifier

**Returns:**
- `number`: Scale value (default: 1.0)

## Module System

### ModuleBase

All RetailUI modules inherit from `RUI.ModuleBase` which provides standardized lifecycle management.

#### Module Lifecycle

1. **Initialize** - Set up module dependencies and basic configuration
2. **Enable** - Register events, create frames, start functionality
3. **Disable** - Clean up resources and unregister events

#### Creating a Module

```lua
local MyModule = RUI:CreateModule("MyModule", {
    moduleVersion = "1.0.0",
    moduleDescription = "My custom module",
    dependencies = {"UnitFrame"},
    optionalDependencies = {"CastingBar"},
    
    OnInitialize = function(self)
        -- Module initialization code
    end,
    
    OnEnable = function(self)
        -- Register events and create frames
        self:RegisterEvent("PLAYER_ENTERING_WORLD")
        self.myFrame = self:CreateUIFrame(100, 100, "MyFrame")
    end,
    
    OnDisable = function(self)
        -- Cleanup handled automatically by ModuleBase
    end,
})
```

#### Module Methods

##### `module:CreateUIFrame(width, height, frameName)`
Creates a UI frame and automatically registers it with memory management.

##### `module:RegisterEvent(event, handler)`
Safely registers an event with automatic cleanup on disable.

##### `module:CreateTimer(key, delay, callback, repeating)`
Creates a managed timer that's automatically cleaned up.

##### `module:GetStatus()`
Returns detailed module status information.

### ModuleManager

Manages module lifecycle and dependencies.

#### Methods

##### `RUI.ModuleManager:RegisterModule(moduleName, moduleInstance)`
Registers a module with the manager.

##### `RUI.ModuleManager:GetStatusReport()`
Returns comprehensive status report for all modules.

##### `RUI.ModuleManager:PrintStatusReport()`
Prints formatted status report to chat.

## Performance & Security

### Performance Monitoring

#### RUI.Performance

Provides performance tracking and optimization recommendations.

##### `RUI.Performance:StartTimer(operation)`
Starts timing an operation.

##### `RUI.Performance:EndTimer(operation)`
Ends timing and records metrics.

##### `RUI.Performance:PrintReport()`
Displays performance report with optimization recommendations.

##### `RUI.Performance:GetSystemMetrics()`
Returns system performance metrics (FPS, memory, latency).

**Example:**
```lua
RUI.Performance:StartTimer("MyOperation")
-- Your code here
RUI.Performance:EndTimer("MyOperation")
```

### Event Throttling

#### RUI.Throttle

Provides event throttling to improve performance.

##### `RUI.Throttle:ThrottledCall(key, delay, callback, ...)`
Executes callback only if enough time has passed since last call.

##### `RUI.Throttle:DebouncedCall(key, delay, callback, ...)`
Executes callback only after delay with no new calls.

**Example:**
```lua
-- Throttle to once per 100ms
RUI.Throttle:ThrottledCall("update_health", 0.1, function()
    UpdateHealthBar()
end)
```

### Security System

#### RUI.Security

Provides input validation and permission management.

##### `RUI.Security:SanitizeInput(input, inputType)`
Sanitizes user input based on type.

**Input Types:**
- `"string"` - Removes dangerous characters, limits length
- `"number"` - Validates and clamps numeric values
- `"frameName"` - Allows only alphanumeric and underscore
- `"command"` - Whitelist validation for commands

##### `RUI.Security:HasPermission(source, action)`
Checks if source has permission for action.

##### `RUI.Security:SecureStore(key, value, source)`
Stores data securely with validation and audit trail.

## Utilities

### Validation

#### RUI.Validator

Provides input validation utilities.

##### `RUI.Validator:ValidateScale(scale)`
Validates scale input (0.1 - 5.0).

**Returns:** `success (boolean), result (number|string)`

##### `RUI.Validator:ValidateFrameName(name)`
Validates frame name format and length.

##### `RUI.Validator:ValidateFrame(frame)`
Validates frame object integrity.

### Memory Management

#### RUI.Memory

Provides automatic memory management and cleanup.

##### `RUI.Memory:RegisterFrame(frame, module, frameType)`
Registers frame for lifecycle management.

##### `RUI.Memory:CleanupModule(moduleName)`
Cleans up all resources for a module.

##### `RUI.Memory:GetMemoryReport()`
Returns detailed memory usage report.

### Logging

#### RUI.Logger

Provides multi-level logging system.

##### Log Levels
- `ERROR` - Critical errors
- `WARN` - Warnings and issues
- `INFO` - General information
- `DEBUG` - Detailed debugging info

##### Methods
```lua
RUI.Logger:Error("Error message: %s", errorDetails)
RUI.Logger:Warn("Warning: %s", warningMessage)
RUI.Logger:Info("Information: %s", infoMessage)
RUI.Logger:Debug("Debug: %s", debugDetails)
```

## Events

### Custom Events

RetailUI fires custom events for module communication:

- `RETAILUI_MODULE_LOADED` - When a module is loaded
- `RETAILUI_MODULE_ENABLED` - When a module is enabled
- `RETAILUI_MODULE_DISABLED` - When a module is disabled
- `RETAILUI_FRAME_CREATED` - When a UI frame is created
- `RETAILUI_SETTINGS_CHANGED` - When settings are modified

### Event Handling

```lua
-- Register for custom events
local frame = CreateFrame("Frame")
frame:RegisterEvent("RETAILUI_MODULE_LOADED")
frame:SetScript("OnEvent", function(self, event, moduleName)
    if event == "RETAILUI_MODULE_LOADED" then
        print("Module loaded:", moduleName)
    end
end)
```

## Examples

### Creating a Simple Module

```lua
local MyModule = RUI:CreateModule("MyModule", {
    moduleVersion = "1.0.0",
    moduleDescription = "Example module",
    
    OnEnable = function(self)
        -- Create a simple frame
        self.frame = self:CreateUIFrame(200, 100, "MyModuleFrame")
        self.frame:SetPoint("CENTER", UIParent, "CENTER", 0, 0)
        
        -- Add some text
        local text = self.frame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
        text:SetPoint("CENTER")
        text:SetText("Hello RetailUI!")
        
        -- Register for events
        self:RegisterEvent("PLAYER_ENTERING_WORLD", function()
            print("Player entered world!")
        end)
    end,
})
```

### Performance Monitoring

```lua
-- Monitor function performance
local function MyExpensiveFunction()
    RUI.Performance:StartTimer("MyExpensiveFunction")
    
    -- Your code here
    for i = 1, 1000000 do
        -- Some work
    end
    
    RUI.Performance:EndTimer("MyExpensiveFunction")
end

-- Check performance report
RUI.Performance:PrintReport()
```

### Secure Data Handling

```lua
-- Safely handle user input
local function HandleUserInput(userInput)
    -- Sanitize input
    local safeInput = RUI.Security:SanitizeInput(userInput, "string")
    if not safeInput then
        RUI.Logger:Error("Invalid input provided")
        return
    end
    
    -- Store securely
    if RUI.Security:SecureStore("userSetting", safeInput, "MyModule") then
        RUI.Logger:Info("Setting saved successfully")
    end
end
```

For more examples and advanced usage, see the [Developer Guide](Developer_Guide.md).
