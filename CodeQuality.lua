--[[
    Copyright (c) Dmitriy. All rights reserved.
    Licensed under the MIT license. See LICENSE file in the project root for details.
]]

-- Code quality enforcement and architectural consistency system

RUI.CodeQuality = {
    -- Code style rules
    styleRules = {
        maxLineLength = 120,
        indentSize = 4,
        maxFunctionLength = 50,
        maxFileLength = 1000,
        namingConventions = {
            functions = "camelCase",
            variables = "camelCase",
            constants = "UPPER_CASE",
            modules = "PascalCase"
        }
    },
    
    -- Dependency tracking
    dependencies = {},
    
    -- Initialize code quality system
    Initialize = function(self)
        self:ScanDependencies()
        self:ValidateArchitecture()
        RUI.Logger:Info("Code quality system initialized")
    end,
    
    -- Scan and validate dependencies
    ScanDependencies = function(self)
        self.dependencies = {
            core = {
                "LibStub",
                "AceAddon-3.0",
                "AceConfig-3.0",
                "AceDB-3.0",
                "AceConsole-3.0"
            },
            modules = {
                UnitFrame = {"AceEvent-3.0", "AceHook-3.0"},
                ActionBar = {"AceEvent-3.0", "AceHook-3.0"},
                CastingBar = {"AceEvent-3.0"},
                Minimap = {"AceEvent-3.0"},
                BuffFrame = {"AceEvent-3.0"},
                QuestLog = {"AceEvent-3.0"},
                QuestTracker = {"AceEvent-3.0", "AceHook-3.0"},
                EditorMode = {"AceEvent-3.0"}
            }
        }
        
        -- Validate core dependencies
        for _, dep in pairs(self.dependencies.core) do
            if not LibStub:GetLibrary(dep, true) then
                RUI.Logger:Error("Missing core dependency: %s", dep)
            end
        end
        
        -- Validate module dependencies
        for moduleName, deps in pairs(self.dependencies.modules) do
            local module = RUI.SafeOps:GetModuleSafely(moduleName)
            if module then
                for _, dep in pairs(deps) do
                    if not LibStub:GetLibrary(dep, true) then
                        RUI.Logger:Warn("Module %s missing dependency: %s", moduleName, dep)
                    end
                end
            end
        end
    end,
    
    -- Validate architectural consistency
    ValidateArchitecture = function(self)
        local issues = {}
        
        -- Check module structure
        local report = RUI.ModuleManager:GetStatusReport()
        for moduleName, status in pairs(report.modules) do
            local module = RUI.SafeOps:GetModuleSafely(moduleName)
            if module then
                -- Check required methods
                local requiredMethods = {"GetStatus", "OnEnable", "OnDisable"}
                for _, method in pairs(requiredMethods) do
                    if not module[method] then
                        table.insert(issues, "Module " .. moduleName .. " missing method: " .. method)
                    end
                end
                
                -- Check module metadata
                if not module.moduleName then
                    table.insert(issues, "Module " .. moduleName .. " missing moduleName property")
                end
                
                if not module.moduleVersion then
                    table.insert(issues, "Module " .. moduleName .. " missing moduleVersion property")
                end
            end
        end
        
        -- Check global namespace pollution
        local globalFunctions = {
            "CreateUIFrame", "ShowUIFrame", "HideUIFrame", 
            "SaveUIFramePosition", "SaveUIFrameScale", "GetUIFrameScale"
        }
        
        for _, funcName in pairs(globalFunctions) do
            if _G[funcName] and type(_G[funcName]) == "function" then
                table.insert(issues, "Global function pollution detected: " .. funcName)
            end
        end
        
        -- Report issues
        if #issues > 0 then
            RUI.Logger:Warn("Architecture validation found %d issues:", #issues)
            for _, issue in pairs(issues) do
                RUI.Logger:Warn("  - %s", issue)
            end
        else
            RUI.Logger:Info("Architecture validation passed")
        end
        
        return #issues == 0
    end,
    
    -- Validate naming conventions
    ValidateNamingConventions = function(self, scope)
        local violations = {}
        
        if scope == "modules" or scope == "all" then
            -- Check module naming
            local report = RUI.ModuleManager:GetStatusReport()
            for moduleName, _ in pairs(report.modules) do
                if not self:IsValidPascalCase(moduleName) then
                    table.insert(violations, "Module name not PascalCase: " .. moduleName)
                end
            end
        end
        
        if scope == "constants" or scope == "all" then
            -- Check constants naming
            for key, _ in pairs(RUI.Constants) do
                if not self:IsValidUpperCase(key) then
                    table.insert(violations, "Constant not UPPER_CASE: " .. key)
                end
            end
        end
        
        return violations
    end,
    
    -- Check if string is valid PascalCase
    IsValidPascalCase = function(self, str)
        return string.match(str, "^[A-Z][a-zA-Z0-9]*$") ~= nil
    end,
    
    -- Check if string is valid UPPER_CASE
    IsValidUpperCase = function(self, str)
        return string.match(str, "^[A-Z][A-Z0-9_]*$") ~= nil
    end,
    
    -- Check if string is valid camelCase
    IsValidCamelCase = function(self, str)
        return string.match(str, "^[a-z][a-zA-Z0-9]*$") ~= nil
    end,
    
    -- Analyze code complexity
    AnalyzeComplexity = function(self)
        local analysis = {
            totalModules = 0,
            totalFunctions = 0,
            averageComplexity = 0,
            highComplexityFunctions = {}
        }
        
        -- Analyze modules
        local report = RUI.ModuleManager:GetStatusReport()
        analysis.totalModules = report.totalModules
        
        -- Estimate function count and complexity
        for moduleName, status in pairs(report.modules) do
            local module = RUI.SafeOps:GetModuleSafely(moduleName)
            if module then
                local functionCount = 0
                for key, value in pairs(module) do
                    if type(value) == "function" then
                        functionCount = functionCount + 1
                    end
                end
                analysis.totalFunctions = analysis.totalFunctions + functionCount
            end
        end
        
        -- Calculate average complexity (simplified metric)
        if analysis.totalModules > 0 then
            analysis.averageComplexity = analysis.totalFunctions / analysis.totalModules
        end
        
        return analysis
    end,
    
    -- Generate code quality report
    GenerateQualityReport = function(self)
        local report = {
            timestamp = date("%Y-%m-%d %H:%M:%S"),
            architecture = self:ValidateArchitecture(),
            dependencies = self.dependencies,
            complexity = self:AnalyzeComplexity(),
            namingViolations = self:ValidateNamingConventions("all"),
            recommendations = {}
        }
        
        -- Generate recommendations
        if not report.architecture then
            table.insert(report.recommendations, "Fix architectural consistency issues")
        end
        
        if #report.namingViolations > 0 then
            table.insert(report.recommendations, "Address naming convention violations")
        end
        
        if report.complexity.averageComplexity > 20 then
            table.insert(report.recommendations, "Consider refactoring high-complexity modules")
        end
        
        if #report.recommendations == 0 then
            table.insert(report.recommendations, "Code quality is excellent!")
        end
        
        return report
    end,
    
    -- Print code quality report
    PrintQualityReport = function(self)
        local report = self:GenerateQualityReport()
        
        RUI.Logger:Info("=== Code Quality Report ===")
        RUI.Logger:Info("Generated: %s", report.timestamp)
        RUI.Logger:Info("Architecture Valid: %s", report.architecture and "✅" or "❌")
        RUI.Logger:Info("Total Modules: %d", report.complexity.totalModules)
        RUI.Logger:Info("Total Functions: %d", report.complexity.totalFunctions)
        RUI.Logger:Info("Average Complexity: %.1f functions/module", report.complexity.averageComplexity)
        
        if #report.namingViolations > 0 then
            RUI.Logger:Warn("Naming Violations:")
            for _, violation in pairs(report.namingViolations) do
                RUI.Logger:Warn("  - %s", violation)
            end
        end
        
        RUI.Logger:Info("Recommendations:")
        for _, rec in pairs(report.recommendations) do
            RUI.Logger:Info("  💡 %s", rec)
        end
    end,
    
    -- Enforce code standards
    EnforceStandards = function(self)
        local enforced = 0
        
        -- Check for deprecated patterns
        local deprecatedPatterns = {
            {pattern = "CreateFrame%(", replacement = "self:CreateFrame(", context = "modules"},
            {pattern = "RegisterEvent%(", replacement = "self:RegisterEvent(", context = "modules"}
        }
        
        -- This would typically scan actual source files
        -- For now, we'll just validate current state
        
        RUI.Logger:Info("Code standards enforcement completed: %d issues addressed", enforced)
        return enforced
    end,
    
    -- Get dependency graph
    GetDependencyGraph = function(self)
        local graph = {
            nodes = {},
            edges = {}
        }
        
        -- Add core as root node
        table.insert(graph.nodes, {id = "Core", type = "core"})
        
        -- Add modules as nodes
        local report = RUI.ModuleManager:GetStatusReport()
        for moduleName, _ in pairs(report.modules) do
            table.insert(graph.nodes, {id = moduleName, type = "module"})
            
            -- Add edge from core to module
            table.insert(graph.edges, {from = "Core", to = moduleName})
            
            -- Add dependencies if any
            local module = RUI.SafeOps:GetModuleSafely(moduleName)
            if module and module.dependencies then
                for _, dep in pairs(module.dependencies) do
                    table.insert(graph.edges, {from = dep, to = moduleName})
                end
            end
        end
        
        return graph
    end,
}

-- Initialize code quality system
local function InitializeCodeQuality()
    if RUI and RUI.CodeQuality then
        RUI.CodeQuality:Initialize()
    end
end

-- Register for delayed initialization
local cqFrame = CreateFrame("Frame")
cqFrame:RegisterEvent("ADDON_LOADED")
cqFrame:SetScript("OnEvent", function(self, event, addonName)
    if addonName == "RetailUI" then
        -- Delay initialization to ensure all modules are loaded
        RUI.Compat.After(3.0, InitializeCodeQuality)
        self:UnregisterEvent("ADDON_LOADED")
    end
end)
