--[[
    Copyright (c) Dmitriy. All rights reserved.
    Licensed under the MIT license. See LICENSE file in the project root for details.
]]

local RUI = LibStub('AceAddon-3.0'):GetAddon('RetailUI')
local AceConfigDialog = LibStub("AceConfigDialog-3.0")

-- Helper functions for configuration
local function GetFrameModule(frameName)
    if frameName:find("actionBar") or frameName == "bagsBar" or frameName == "microMenuBar" or frameName == "repExpBar" then
        return RUI:GetModule("ActionBar")
    elseif frameName:find("boss") or frameName == "player" or frameName == "target" or frameName == "focus" or frameName == "pet" or frameName == "targetOfTarget" then
        return RUI:GetModule("UnitFrame")
    elseif frameName == "castingBar" then
        return RUI:GetModule("CastingBar")
    elseif frameName == "minimap" then
        return RUI:GetModule("Minimap")
    elseif frameName == "questTracker" then
        return RUI:GetModule("QuestTracker")
    elseif frameName == "buffs" then
        return RUI:GetModule("BuffFrame")
    end
    return nil
end

local function RefreshFrame(frameName)
    local module = GetFrameModule(frameName)
    if module and module.UpdateWidgets then
        module:UpdateWidgets()
    end
end

local function GetFramePosition(frameName)
    local widget = RUI.DB.profile.widgets[frameName]
    if not widget then return "Not Set", 0, 0 end
    return widget.anchor or "CENTER", widget.posX or 0, widget.posY or 0
end

local function GetFrameScale(frameName)
    local widget = RUI.DB.profile.widgets[frameName]
    return (widget and widget.scale) or 1.0
end

local function SetFrameScale(frameName, scale)
    if not RUI.DB.profile.widgets[frameName] then
        RUI.DB.profile.widgets[frameName] = {}
    end
    RUI.DB.profile.widgets[frameName].scale = scale
    RefreshFrame(frameName)
end

-- Main options table for the settings GUI
RUI.options = {
    name = "RetailUI",
    handler = RUI,
    type = "group",
    childGroups = "tab",
    args = {
        general = {
            name = "🏠 General",
            type = "group",
            order = 1,
            args = {
                header = {
                    name = "|cFF00FF00RetailUI Configuration|r",
                    type = "header",
                    order = 0,
                },
                description = {
                    name = "|cFFFFFFFFWelcome to RetailUI! This addon transforms your Classic WoW interface to look and feel like Retail WoW.\n\n|cFFFFFF00Quick Start:|r\n• Use the |cFF00FF00Toggle Edit Mode|r button below to move UI elements\n• Configure individual frames in the |cFF00FF00Frame Settings|r tab\n• Try different |cFF00FF00Presets|r for quick setup\n• Use |cFF00FF00Profiles|r to save and share your layouts|r",
                    type = "description",
                    order = 1,
                },
                spacer1 = { name = "", type = "description", order = 2 },

                -- Quick Actions
                quickActions = {
                    name = "Quick Actions",
                    type = "group",
                    order = 3,
                    inline = true,
                    args = {
                        edit = {
                            name = "Toggle Edit Mode",
                            desc = "Enable or disable edit mode to move UI elements around. Right-click frames to access quick options.",
                            type = 'execute',
                            order = 1,
                            func = function()
                                local EditorMode = RUI:GetModule('EditorMode')
                                if EditorMode:IsShown() then
                                    EditorMode:Hide()
                                    print("|cFF00FF00RetailUI:|r Edit mode disabled")
                                else
                                    EditorMode:Show()
                                    print("|cFF00FF00RetailUI:|r Edit mode enabled - drag frames to reposition them!")
                                end
                            end,
                        },
                        reloadUI = {
                            name = "Reload UI",
                            desc = "Reload the user interface to apply certain changes",
                            type = 'execute',
                            order = 2,
                            func = function()
                                ReloadUI()
                            end,
                        },
                        resetAll = {
                            name = "Reset All to Defaults",
                            desc = "Reset all UI elements to their default positions and settings",
                            type = 'execute',
                            order = 3,
                            confirm = true,
                            confirmText = "Are you sure you want to reset ALL settings to defaults? This cannot be undone!",
                            func = function()
                                -- Reset all modules
                                local modules = {
                                    RUI:GetModule("UnitFrame"),
                                    RUI:GetModule("ActionBar"),
                                    RUI:GetModule("CastingBar"),
                                    RUI:GetModule("Minimap"),
                                    RUI:GetModule("QuestTracker"),
                                    RUI:GetModule("BuffFrame")
                                }

                                for _, module in pairs(modules) do
                                    if module and module.LoadDefaultSettings then
                                        module:LoadDefaultSettings()
                                        if module.UpdateWidgets then
                                            module:UpdateWidgets()
                                        end
                                    end
                                end

                                -- Reset grid settings
                                RUI.DB.profile.gridSize = 32
                                RUI.DB.profile.gridAlpha = 0.4
                                RUI.DB.profile.showCenterDots = true

                                print("|cFF00FF00RetailUI:|r All settings reset to defaults!")
                            end,
                        },
                    },
                },

                spacer2 = { name = "", type = "description", order = 4 },

                -- Status Information
                statusInfo = {
                    name = "Status Information",
                    type = "group",
                    order = 5,
                    inline = true,
                    args = {
                        version = {
                            name = "Version",
                            type = "description",
                            order = 1,
                            fontSize = "medium",
                            get = function() return "|cFFFFFFFFRetailUI v1.4.0|r" end,
                        },
                        editorStatus = {
                            name = "Editor Mode",
                            type = "description",
                            order = 2,
                            fontSize = "medium",
                            get = function()
                                local EditorMode = RUI:GetModule('EditorMode')
                                if EditorMode and EditorMode:IsShown() then
                                    return "|cFF00FF00Active|r"
                                else
                                    return "|cFFFF0000Inactive|r"
                                end
                            end,
                        },
                        frameCount = {
                            name = "Configured Frames",
                            type = "description",
                            order = 3,
                            fontSize = "medium",
                            get = function()
                                local count = 0
                                for _ in pairs(RUI.DB.profile.widgets) do
                                    count = count + 1
                                end
                                return "|cFFFFFFFF" .. count .. " frames|r"
                            end,
                        },
                    },
                },
            },
        },
        gridSnapping = {
            name = "⚡ Grid & Snapping",
            type = "group",
            order = 2,
            args = {
                header = {
                    name = "Grid and Snapping Configuration",
                    type = "header",
                    order = 0,
                },
                description = {
                    name = "|cFFFFFFFFConfigure the grid system and snapping behavior for precise frame positioning.\n\n|cFFFFFF00Note:|r Some grid changes require a UI reload to take effect.|r",
                    type = "description",
                    order = 1,
                },

                -- Grid Visual Settings
                gridVisual = {
                    name = "Grid Visual Settings",
                    type = "group",
                    order = 2,
                    inline = true,
                    args = {
                        gridSize = {
                            name = "Grid Size",
                            desc = "Size of each grid cell in pixels. Smaller values = more precise positioning.\n\n|cFFFF0000Requires UI reload to take effect!|r",
                            type = "range",
                            order = 1,
                            min = 8,
                            max = 128,
                            step = 8,
                            bigStep = 16,
                            get = function() return RUI.DB.profile.gridSize or 32 end,
                            set = function(info, value)
                                RUI.DB.profile.gridSize = value
                                print("|cFF00FF00RetailUI:|r Grid size changed to " .. value .. "px. |cFFFF0000Please reload your UI!|r")
                            end,
                        },
                        gridAlpha = {
                            name = "Grid Transparency",
                            desc = "How visible the grid lines are in edit mode. 0.1 = barely visible, 1.0 = fully opaque",
                            type = "range",
                            order = 2,
                            min = 0.05,
                            max = 1.0,
                            step = 0.05,
                            isPercent = true,
                            get = function() return RUI.DB.profile.gridAlpha or 0.4 end,
                            set = function(info, value)
                                RUI.DB.profile.gridAlpha = value
                                -- Update grid if editor mode is active
                                local EditorMode = RUI:GetModule('EditorMode')
                                if EditorMode and EditorMode:IsShown() then
                                    EditorMode:RefreshGrid()
                                end
                            end,
                        },
                        gridColor = {
                            name = "Grid Color",
                            desc = "Color of the grid lines",
                            type = "color",
                            order = 3,
                            hasAlpha = false,
                            get = function()
                                local color = RUI.DB.profile.gridColor or {1, 1, 1}
                                return color[1], color[2], color[3]
                            end,
                            set = function(info, r, g, b)
                                RUI.DB.profile.gridColor = {r, g, b}
                                local EditorMode = RUI:GetModule('EditorMode')
                                if EditorMode and EditorMode:IsShown() then
                                    EditorMode:RefreshGrid()
                                end
                            end,
                        },
                    },
                },

                -- Snapping Settings
                snappingSettings = {
                    name = "Snapping Behavior",
                    type = "group",
                    order = 3,
                    inline = true,
                    args = {
                        enableSnapping = {
                            name = "Enable Grid Snapping",
                            desc = "Automatically snap frames to grid intersections when moving them",
                            type = "toggle",
                            order = 1,
                            get = function() return RUI.DB.profile.enableSnapping ~= false end,
                            set = function(info, value)
                                RUI.DB.profile.enableSnapping = value
                            end,
                        },
                        snapTolerance = {
                            name = "Snap Tolerance",
                            desc = "How close to a grid line before snapping occurs (in pixels)",
                            type = "range",
                            order = 2,
                            min = 1,
                            max = 32,
                            step = 1,
                            disabled = function() return RUI.DB.profile.enableSnapping == false end,
                            get = function() return RUI.DB.profile.snapTolerance or 16 end,
                            set = function(info, value)
                                RUI.DB.profile.snapTolerance = value
                            end,
                        },
                        centerBasedSnapping = {
                            name = "Center-Based Snapping",
                            desc = "Snap frames by their center point instead of anchor point",
                            type = "toggle",
                            order = 3,
                            disabled = function() return RUI.DB.profile.enableSnapping == false end,
                            get = function() return RUI.DB.profile.centerBasedSnapping ~= false end,
                            set = function(info, value)
                                RUI.DB.profile.centerBasedSnapping = value
                            end,
                        },
                    },
                },

                -- Center Dots Settings
                centerDots = {
                    name = "Center Guidance",
                    type = "group",
                    order = 4,
                    inline = true,
                    args = {
                        showCenterDots = {
                            name = "Show Center Dots",
                            desc = "Display red dots at the center of frames in edit mode for visual guidance",
                            type = "toggle",
                            order = 1,
                            get = function() return RUI.DB.profile.showCenterDots ~= false end,
                            set = function(info, value)
                                RUI.DB.profile.showCenterDots = value
                                -- Update center dots if editor mode is active
                                local EditorMode = RUI:GetModule('EditorMode')
                                if EditorMode and EditorMode:IsShown() then
                                    if value then
                                        RUI:ShowCenterDots()
                                    else
                                        RUI:HideCenterDots()
                                    end
                                end
                            end,
                        },
                        centerDotSize = {
                            name = "Center Dot Size",
                            desc = "Size of the center guidance dots",
                            type = "range",
                            order = 2,
                            min = 2,
                            max = 12,
                            step = 1,
                            disabled = function() return RUI.DB.profile.showCenterDots == false end,
                            get = function() return RUI.DB.profile.centerDotSize or 6 end,
                            set = function(info, value)
                                RUI.DB.profile.centerDotSize = value
                                -- Refresh center dots if active
                                local EditorMode = RUI:GetModule('EditorMode')
                                if EditorMode and EditorMode:IsShown() and RUI.DB.profile.showCenterDots then
                                    RUI:HideCenterDots()
                                    RUI:ShowCenterDots()
                                end
                            end,
                        },
                        centerDotColor = {
                            name = "Center Dot Color",
                            desc = "Color of the center guidance dots",
                            type = "color",
                            order = 3,
                            hasAlpha = true,
                            disabled = function() return RUI.DB.profile.showCenterDots == false end,
                            get = function()
                                local color = RUI.DB.profile.centerDotColor or {1, 0, 0, 0.9}
                                return color[1], color[2], color[3], color[4]
                            end,
                            set = function(info, r, g, b, a)
                                RUI.DB.profile.centerDotColor = {r, g, b, a}
                                -- Refresh center dots if active
                                local EditorMode = RUI:GetModule('EditorMode')
                                if EditorMode and EditorMode:IsShown() and RUI.DB.profile.showCenterDots then
                                    RUI:HideCenterDots()
                                    RUI:ShowCenterDots()
                                end
                            end,
                        },
                    },
                },

                -- Grid Presets
                gridPresets = {
                    name = "Grid Presets",
                    type = "group",
                    order = 5,
                    inline = true,
                    args = {
                        presetFine = {
                            name = "Fine (16px)",
                            desc = "Small grid for precise positioning",
                            type = "execute",
                            order = 1,
                            func = function()
                                RUI.DB.profile.gridSize = 16
                                RUI.DB.profile.gridAlpha = 0.3
                                print("|cFF00FF00RetailUI:|r Fine grid preset applied. |cFFFF0000Please reload your UI!|r")
                            end,
                        },
                        presetNormal = {
                            name = "Normal (32px)",
                            desc = "Standard grid size (default)",
                            type = "execute",
                            order = 2,
                            func = function()
                                RUI.DB.profile.gridSize = 32
                                RUI.DB.profile.gridAlpha = 0.4
                                print("|cFF00FF00RetailUI:|r Normal grid preset applied. |cFFFF0000Please reload your UI!|r")
                            end,
                        },
                        presetCoarse = {
                            name = "Coarse (64px)",
                            desc = "Large grid for quick positioning",
                            type = "execute",
                            order = 3,
                            func = function()
                                RUI.DB.profile.gridSize = 64
                                RUI.DB.profile.gridAlpha = 0.5
                                print("|cFF00FF00RetailUI:|r Coarse grid preset applied. |cFFFF0000Please reload your UI!|r")
                            end,
                        },
                    },
                },
            },
        },

        frameSettings = {
            name = "🖼️ Frame Settings",
            type = "group",
            order = 3,
            childGroups = "tab",
            args = {
                header = {
                    name = "Individual Frame Configuration",
                    type = "header",
                    order = 0,
                },
                description = {
                    name = "|cFFFFFFFFConfigure position, scale, and visibility for each UI frame individually.\n\n|cFFFFFF00Tip:|r Use Edit Mode for visual positioning, then fine-tune here with precise values.|r",
                    type = "description",
                    order = 1,
                },

                -- Unit Frames
                unitFrames = {
                    name = "Unit Frames",
                    type = "group",
                    order = 2,
                    args = {
                        unitFramesHeader = {
                            name = "Unit Frame Configuration",
                            type = "header",
                            order = 0,
                        },
                        unitFramesDesc = {
                            name = "|cFFFFFFFFConfigure player, target, focus, pet, and boss frames.|r",
                            type = "description",
                            order = 1,
                        },

                        -- Player Frame
                        playerFrame = {
                            name = "Player Frame",
                            type = "group",
                            order = 2,
                            inline = true,
                            args = {
                                playerScale = {
                                    name = "Scale",
                                    desc = "Size multiplier for the player frame",
                                    type = 'range',
                                    order = 1,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("player") end,
                                    set = function(info, value) SetFrameScale("player", value) end,
                                },
                                playerPosition = {
                                    name = "Position Info",
                                    type = "description",
                                    order = 2,
                                    fontSize = "medium",
                                    get = function()
                                        local anchor, x, y = GetFramePosition("player")
                                        return string.format("|cFFFFFFFFAnchor:|r %s |cFFFFFFFFX:|r %.0f |cFFFFFFFFY:|r %.0f", anchor, x, y)
                                    end,
                                },
                                playerReset = {
                                    name = "Reset to Default",
                                    desc = "Reset player frame to default position and scale",
                                    type = "execute",
                                    order = 3,
                                    func = function()
                                        RUI.DB.profile.widgets.player = { anchor = "TOPLEFT", posX = 5, posY = -20, scale = 1 }
                                        RefreshFrame("player")
                                        print("|cFF00FF00RetailUI:|r Player frame reset to default")
                                    end,
                                },
                            },
                        },

                        -- Target Frame
                        targetFrame = {
                            name = "Target Frame",
                            type = "group",
                            order = 3,
                            inline = true,
                            args = {
                                targetScale = {
                                    name = "Scale",
                                    desc = "Size multiplier for the target frame",
                                    type = 'range',
                                    order = 1,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("target") end,
                                    set = function(info, value) SetFrameScale("target", value) end,
                                },
                                targetPosition = {
                                    name = "Position Info",
                                    type = "description",
                                    order = 2,
                                    fontSize = "medium",
                                    get = function()
                                        local anchor, x, y = GetFramePosition("target")
                                        return string.format("|cFFFFFFFFAnchor:|r %s |cFFFFFFFFX:|r %.0f |cFFFFFFFFY:|r %.0f", anchor, x, y)
                                    end,
                                },
                                targetReset = {
                                    name = "Reset to Default",
                                    desc = "Reset target frame to default position and scale",
                                    type = "execute",
                                    order = 3,
                                    func = function()
                                        RUI.DB.profile.widgets.target = { anchor = "TOPLEFT", posX = 215, posY = -20, scale = 1 }
                                        RefreshFrame("target")
                                        print("|cFF00FF00RetailUI:|r Target frame reset to default")
                                    end,
                                },
                            },
                        },

                        -- Focus Frame
                        focusFrame = {
                            name = "Focus Frame",
                            type = "group",
                            order = 4,
                            inline = true,
                            args = {
                                focusScale = {
                                    name = "Scale",
                                    desc = "Size multiplier for the focus frame",
                                    type = 'range',
                                    order = 1,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("focus") end,
                                    set = function(info, value) SetFrameScale("focus", value) end,
                                },
                                focusPosition = {
                                    name = "Position Info",
                                    type = "description",
                                    order = 2,
                                    fontSize = "medium",
                                    get = function()
                                        local anchor, x, y = GetFramePosition("focus")
                                        return string.format("|cFFFFFFFFAnchor:|r %s |cFFFFFFFFX:|r %.0f |cFFFFFFFFY:|r %.0f", anchor, x, y)
                                    end,
                                },
                                focusReset = {
                                    name = "Reset to Default",
                                    desc = "Reset focus frame to default position and scale",
                                    type = "execute",
                                    order = 3,
                                    func = function()
                                        RUI.DB.profile.widgets.focus = { anchor = "TOPLEFT", posX = 105, posY = -165, scale = 1 }
                                        RefreshFrame("focus")
                                        print("|cFF00FF00RetailUI:|r Focus frame reset to default")
                                    end,
                                },
                            },
                        },

                        -- Pet Frame
                        petFrame = {
                            name = "Pet Frame",
                            type = "group",
                            order = 5,
                            inline = true,
                            args = {
                                petScale = {
                                    name = "Scale",
                                    desc = "Size multiplier for the pet frame",
                                    type = 'range',
                                    order = 1,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("pet") end,
                                    set = function(info, value) SetFrameScale("pet", value) end,
                                },
                                petPosition = {
                                    name = "Position Info",
                                    type = "description",
                                    order = 2,
                                    fontSize = "medium",
                                    get = function()
                                        local anchor, x, y = GetFramePosition("pet")
                                        return string.format("|cFFFFFFFFAnchor:|r %s |cFFFFFFFFX:|r %.0f |cFFFFFFFFY:|r %.0f", anchor, x, y)
                                    end,
                                },
                                petReset = {
                                    name = "Reset to Default",
                                    desc = "Reset pet frame to default position and scale",
                                    type = "execute",
                                    order = 3,
                                    func = function()
                                        RUI.DB.profile.widgets.pet = { anchor = "TOPLEFT", posX = 90, posY = -105, scale = 1 }
                                        RefreshFrame("pet")
                                        print("|cFF00FF00RetailUI:|r Pet frame reset to default")
                                    end,
                                },
                            },
                        },

                        -- Target of Target Frame
                        totFrame = {
                            name = "Target of Target Frame",
                            type = "group",
                            order = 6,
                            inline = true,
                            args = {
                                totScale = {
                                    name = "Scale",
                                    desc = "Size multiplier for the target of target frame",
                                    type = 'range',
                                    order = 1,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("targetOfTarget") end,
                                    set = function(info, value) SetFrameScale("targetOfTarget", value) end,
                                },
                                totPosition = {
                                    name = "Position Info",
                                    type = "description",
                                    order = 2,
                                    fontSize = "medium",
                                    get = function()
                                        local anchor, x, y = GetFramePosition("targetOfTarget")
                                        return string.format("|cFFFFFFFFAnchor:|r %s |cFFFFFFFFX:|r %.0f |cFFFFFFFFY:|r %.0f", anchor, x, y)
                                    end,
                                },
                                totReset = {
                                    name = "Reset to Default",
                                    desc = "Reset target of target frame to default position and scale",
                                    type = "execute",
                                    order = 3,
                                    func = function()
                                        RUI.DB.profile.widgets.targetOfTarget = { anchor = "TOPLEFT", posX = 370, posY = -80, scale = 1 }
                                        RefreshFrame("targetOfTarget")
                                        print("|cFF00FF00RetailUI:|r Target of Target frame reset to default")
                                    end,
                                },
                            },
                        },
                    },
                },

                -- Action Bars
                actionBars = {
                    name = "Action Bars",
                    type = "group",
                    order = 3,
                    args = {
                        actionBarsHeader = {
                            name = "Action Bar Configuration",
                            type = "header",
                            order = 0,
                        },
                        actionBarsDesc = {
                            name = "|cFFFFFFFFConfigure main action bars, bags, micro menu, and experience bars.|r",
                            type = "description",
                            order = 1,
                        },

                        -- Main Action Bars
                        mainActionBars = {
                            name = "Main Action Bars (1-5)",
                            type = "group",
                            order = 2,
                            inline = true,
                            args = {
                                actionBarsScale = {
                                    name = "All Action Bars Scale",
                                    desc = "Size multiplier for ALL action bars (1-11)",
                                    type = 'range',
                                    order = 1,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("actionBar1") end,
                                    set = function(info, value)
                                        for i = 1, 11 do
                                            SetFrameScale("actionBar" .. i, value)
                                        end
                                    end,
                                },
                                actionBar1 = {
                                    name = "Action Bar 1 Position",
                                    type = "description",
                                    order = 2,
                                    fontSize = "medium",
                                    get = function()
                                        local anchor, x, y = GetFramePosition("actionBar1")
                                        return string.format("|cFFFFFFFFBar 1 - Anchor:|r %s |cFFFFFFFFX:|r %.0f |cFFFFFFFFY:|r %.0f", anchor, x, y)
                                    end,
                                },
                                actionBar2 = {
                                    name = "Action Bar 2 Position",
                                    type = "description",
                                    order = 3,
                                    fontSize = "medium",
                                    get = function()
                                        local anchor, x, y = GetFramePosition("actionBar2")
                                        return string.format("|cFFFFFFFFBar 2 - Anchor:|r %s |cFFFFFFFFX:|r %.0f |cFFFFFFFFY:|r %.0f", anchor, x, y)
                                    end,
                                },
                                actionBar3 = {
                                    name = "Action Bar 3 Position",
                                    type = "description",
                                    order = 4,
                                    fontSize = "medium",
                                    get = function()
                                        local anchor, x, y = GetFramePosition("actionBar3")
                                        return string.format("|cFFFFFFFFBar 3 - Anchor:|r %s |cFFFFFFFFX:|r %.0f |cFFFFFFFFY:|r %.0f", anchor, x, y)
                                    end,
                                },
                                resetActionBars = {
                                    name = "Reset All Action Bars",
                                    desc = "Reset all action bars to default positions and scale",
                                    type = "execute",
                                    order = 5,
                                    func = function()
                                        -- Main action bars 1-5
                                        for index = 1, 5 do
                                            RUI.DB.profile.widgets['actionBar' .. index] = {
                                                anchor = "BOTTOM",
                                                posX = 0,
                                                posY = 60 + 4 * (index - 1) + 42 * (index - 1),
                                                scale = 1
                                            }
                                        end

                                        -- Special action bars 6-11 with default scale
                                        for index = 6, 11 do
                                            if RUI.DB.profile.widgets['actionBar' .. index] then
                                                RUI.DB.profile.widgets['actionBar' .. index].scale = 1
                                            end
                                        end

                                        -- Refresh all action bars
                                        for index = 1, 11 do
                                            RefreshFrame("actionBar" .. index)
                                        end
                                        print("|cFF00FF00RetailUI:|r All action bars reset to default")
                                    end,
                                },
                            },
                        },

                        -- Individual Action Bar Controls
                        individualActionBars = {
                            name = "Individual Action Bar Controls",
                            type = "group",
                            order = 3,
                            inline = true,
                            args = {
                                actionBar1Scale = {
                                    name = "Action Bar 1 Scale",
                                    desc = "Individual scale for action bar 1",
                                    type = 'range',
                                    order = 1,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("actionBar1") end,
                                    set = function(info, value) SetFrameScale("actionBar1", value) end,
                                },
                                actionBar2Scale = {
                                    name = "Action Bar 2 Scale",
                                    desc = "Individual scale for action bar 2",
                                    type = 'range',
                                    order = 2,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("actionBar2") end,
                                    set = function(info, value) SetFrameScale("actionBar2", value) end,
                                },
                                actionBar3Scale = {
                                    name = "Action Bar 3 Scale",
                                    desc = "Individual scale for action bar 3",
                                    type = 'range',
                                    order = 3,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("actionBar3") end,
                                    set = function(info, value) SetFrameScale("actionBar3", value) end,
                                },
                                actionBar4Scale = {
                                    name = "Action Bar 4 Scale",
                                    desc = "Individual scale for action bar 4",
                                    type = 'range',
                                    order = 4,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("actionBar4") end,
                                    set = function(info, value) SetFrameScale("actionBar4", value) end,
                                },
                                actionBar5Scale = {
                                    name = "Action Bar 5 Scale",
                                    desc = "Individual scale for action bar 5",
                                    type = 'range',
                                    order = 5,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("actionBar5") end,
                                    set = function(info, value) SetFrameScale("actionBar5", value) end,
                                },
                                actionBar6Scale = {
                                    name = "Action Bar 6 (Bonus) Scale",
                                    desc = "Individual scale for action bar 6 (bonus bar)",
                                    type = 'range',
                                    order = 6,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("actionBar6") end,
                                    set = function(info, value) SetFrameScale("actionBar6", value) end,
                                },
                                actionBar7Scale = {
                                    name = "Action Bar 7 (Shapeshift) Scale",
                                    desc = "Individual scale for action bar 7 (shapeshift/stance bar)",
                                    type = 'range',
                                    order = 7,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("actionBar7") end,
                                    set = function(info, value) SetFrameScale("actionBar7", value) end,
                                },
                                actionBar8Scale = {
                                    name = "Action Bar 8 (Pet) Scale",
                                    desc = "Individual scale for action bar 8 (pet bar)",
                                    type = 'range',
                                    order = 8,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("actionBar8") end,
                                    set = function(info, value) SetFrameScale("actionBar8", value) end,
                                },
                                actionBar9Scale = {
                                    name = "Action Bar 9 (Possess) Scale",
                                    desc = "Individual scale for action bar 9 (possess bar)",
                                    type = 'range',
                                    order = 9,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("actionBar9") end,
                                    set = function(info, value) SetFrameScale("actionBar9", value) end,
                                },
                                actionBar10Scale = {
                                    name = "Action Bar 10 (Vehicle) Scale",
                                    desc = "Individual scale for action bar 10 (vehicle bar)",
                                    type = 'range',
                                    order = 10,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("actionBar10") end,
                                    set = function(info, value) SetFrameScale("actionBar10", value) end,
                                },
                                actionBar11Scale = {
                                    name = "Action Bar 11 (Multicast) Scale",
                                    desc = "Individual scale for action bar 11 (multicast bar)",
                                    type = 'range',
                                    order = 11,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("actionBar11") end,
                                    set = function(info, value) SetFrameScale("actionBar11", value) end,
                                },
                            },
                        },

                        -- Other UI Elements
                        otherElements = {
                            name = "Other UI Elements",
                            type = "group",
                            order = 3,
                            inline = true,
                            args = {
                                bagsBarScale = {
                                    name = "Bags Bar Scale",
                                    desc = "Size multiplier for the bags bar",
                                    type = 'range',
                                    order = 1,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("bagsBar") end,
                                    set = function(info, value) SetFrameScale("bagsBar", value) end,
                                },
                                microMenuBarScale = {
                                    name = "Micro Menu Scale",
                                    desc = "Size multiplier for the micro menu bar",
                                    type = 'range',
                                    order = 2,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("microMenuBar") end,
                                    set = function(info, value) SetFrameScale("microMenuBar", value) end,
                                },
                                repExpBarScale = {
                                    name = "Rep/Exp Bar Scale",
                                    desc = "Size multiplier for the reputation/experience bar",
                                    type = 'range',
                                    order = 3,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("repExpBar") end,
                                    set = function(info, value) SetFrameScale("repExpBar", value) end,
                                },
                                bagsBar = {
                                    name = "Bags Bar Position",
                                    type = "description",
                                    order = 4,
                                    fontSize = "medium",
                                    get = function()
                                        local anchor, x, y = GetFramePosition("bagsBar")
                                        return string.format("|cFFFFFFFFBags - Anchor:|r %s |cFFFFFFFFX:|r %.0f |cFFFFFFFFY:|r %.0f", anchor, x, y)
                                    end,
                                },
                                microMenuBar = {
                                    name = "Micro Menu Position",
                                    type = "description",
                                    order = 5,
                                    fontSize = "medium",
                                    get = function()
                                        local anchor, x, y = GetFramePosition("microMenuBar")
                                        return string.format("|cFFFFFFFFMicro Menu - Anchor:|r %s |cFFFFFFFFX:|r %.0f |cFFFFFFFFY:|r %.0f", anchor, x, y)
                                    end,
                                },
                                repExpBar = {
                                    name = "Rep/Exp Bar Position",
                                    type = "description",
                                    order = 6,
                                    fontSize = "medium",
                                    get = function()
                                        local anchor, x, y = GetFramePosition("repExpBar")
                                        return string.format("|cFFFFFFFFRep/Exp - Anchor:|r %s |cFFFFFFFFX:|r %.0f |cFFFFFFFFY:|r %.0f", anchor, x, y)
                                    end,
                                },
                                resetOtherElements = {
                                    name = "Reset Other Elements",
                                    desc = "Reset bags, micro menu, and rep/exp bars to defaults",
                                    type = "execute",
                                    order = 7,
                                    func = function()
                                        RUI.DB.profile.widgets.microMenuBar = { anchor = "BOTTOMRIGHT", posX = 50, posY = 10, scale = 1 }
                                        RUI.DB.profile.widgets.bagsBar = { anchor = "BOTTOMRIGHT", posX = 13, posY = 45, scale = 1 }
                                        RUI.DB.profile.widgets.repExpBar = { anchor = "BOTTOM", posX = 0, posY = 35, scale = 1 }
                                        RefreshFrame("microMenuBar")
                                        RefreshFrame("bagsBar")
                                        RefreshFrame("repExpBar")
                                        print("|cFF00FF00RetailUI:|r Other UI elements reset to default")
                                    end,
                                },
                            },
                        },
                    },
                },

                -- Other Frames
                otherFrames = {
                    name = "Other Frames",
                    type = "group",
                    order = 4,
                    args = {
                        otherFramesHeader = {
                            name = "Miscellaneous Frame Configuration",
                            type = "header",
                            order = 0,
                        },
                        otherFramesDesc = {
                            name = "|cFFFFFFFFConfigure minimap, quest tracker, buffs, and casting bar. All frames now support scaling!|r",
                            type = "description",
                            order = 1,
                        },

                        -- Minimap
                        minimapFrame = {
                            name = "Minimap",
                            type = "group",
                            order = 2,
                            inline = true,
                            args = {
                                minimapScale = {
                                    name = "Scale",
                                    desc = "Size multiplier for the minimap",
                                    type = 'range',
                                    order = 1,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("minimap") end,
                                    set = function(info, value) SetFrameScale("minimap", value) end,
                                },
                                minimapPosition = {
                                    name = "Position Info",
                                    type = "description",
                                    order = 2,
                                    fontSize = "medium",
                                    get = function()
                                        local anchor, x, y = GetFramePosition("minimap")
                                        return string.format("|cFFFFFFFFMinimap - Anchor:|r %s |cFFFFFFFFX:|r %.0f |cFFFFFFFFY:|r %.0f", anchor, x, y)
                                    end,
                                },
                                minimapReset = {
                                    name = "Reset Minimap",
                                    desc = "Reset minimap to default position and scale",
                                    type = "execute",
                                    order = 3,
                                    func = function()
                                        RUI.DB.profile.widgets.minimap = { anchor = "TOPRIGHT", posX = 0, posY = 0, scale = 1 }
                                        RefreshFrame("minimap")
                                        print("|cFF00FF00RetailUI:|r Minimap reset to default")
                                    end,
                                },
                            },
                        },

                        -- Quest Tracker
                        questTrackerFrame = {
                            name = "Quest Tracker",
                            type = "group",
                            order = 3,
                            inline = true,
                            args = {
                                questTrackerScale = {
                                    name = "Scale",
                                    desc = "Size multiplier for the quest tracker",
                                    type = 'range',
                                    order = 1,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("questTracker") end,
                                    set = function(info, value) SetFrameScale("questTracker", value) end,
                                },
                                questTrackerPosition = {
                                    name = "Position Info",
                                    type = "description",
                                    order = 2,
                                    fontSize = "medium",
                                    get = function()
                                        local anchor, x, y = GetFramePosition("questTracker")
                                        return string.format("|cFFFFFFFFQuest Tracker - Anchor:|r %s |cFFFFFFFFX:|r %.0f |cFFFFFFFFY:|r %.0f", anchor, x, y)
                                    end,
                                },
                                questTrackerReset = {
                                    name = "Reset Quest Tracker",
                                    desc = "Reset quest tracker to default position and scale",
                                    type = "execute",
                                    order = 3,
                                    func = function()
                                        RUI.DB.profile.widgets.questTracker = { anchor = "RIGHT", posX = -100, posY = -37, scale = 1 }
                                        RefreshFrame("questTracker")
                                        print("|cFF00FF00RetailUI:|r Quest tracker reset to default")
                                    end,
                                },
                            },
                        },

                        -- Buffs
                        buffsFrame = {
                            name = "Buffs/Debuffs",
                            type = "group",
                            order = 4,
                            inline = true,
                            args = {
                                buffsScale = {
                                    name = "Scale",
                                    desc = "Size multiplier for the buffs/debuffs frame",
                                    type = 'range',
                                    order = 1,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("buffs") end,
                                    set = function(info, value) SetFrameScale("buffs", value) end,
                                },
                                buffsPosition = {
                                    name = "Position Info",
                                    type = "description",
                                    order = 2,
                                    fontSize = "medium",
                                    get = function()
                                        local anchor, x, y = GetFramePosition("buffs")
                                        return string.format("|cFFFFFFFFBuffs - Anchor:|r %s |cFFFFFFFFX:|r %.0f |cFFFFFFFFY:|r %.0f", anchor, x, y)
                                    end,
                                },
                                buffsReset = {
                                    name = "Reset Buffs",
                                    desc = "Reset buffs frame to default position and scale",
                                    type = "execute",
                                    order = 3,
                                    func = function()
                                        RUI.DB.profile.widgets.buffs = { anchor = "TOPRIGHT", posX = -260, posY = -20, scale = 1 }
                                        RefreshFrame("buffs")
                                        print("|cFF00FF00RetailUI:|r Buffs frame reset to default")
                                    end,
                                },
                            },
                        },

                        -- Casting Bar
                        castingBarFrame = {
                            name = "Casting Bar",
                            type = "group",
                            order = 5,
                            inline = true,
                            args = {
                                castingBarScale = {
                                    name = "Scale",
                                    desc = "Size multiplier for the casting bar",
                                    type = 'range',
                                    order = 1,
                                    min = 0.3,
                                    max = 3.0,
                                    step = 0.05,
                                    bigStep = 0.1,
                                    isPercent = true,
                                    get = function() return GetFrameScale("playerCastingBar") end,
                                    set = function(info, value) SetFrameScale("playerCastingBar", value) end,
                                },
                                castingBarPosition = {
                                    name = "Position Info",
                                    type = "description",
                                    order = 2,
                                    fontSize = "medium",
                                    get = function()
                                        local anchor, x, y = GetFramePosition("playerCastingBar")
                                        return string.format("|cFFFFFFFFCasting Bar - Anchor:|r %s |cFFFFFFFFX:|r %.0f |cFFFFFFFFY:|r %.0f", anchor, x, y)
                                    end,
                                },
                                castingBarReset = {
                                    name = "Reset Casting Bar",
                                    desc = "Reset casting bar to default position and scale",
                                    type = "execute",
                                    order = 3,
                                    func = function()
                                        RUI.DB.profile.widgets.playerCastingBar = { anchor = "BOTTOM", posX = 0, posY = 270, scale = 1 }
                                        RefreshFrame("playerCastingBar")
                                        print("|cFF00FF00RetailUI:|r Casting bar reset to default")
                                    end,
                                },
                            },
                        },
                    },
                },
            },
        },

        presets = {
            name = "🎨 Presets",
            type = "group",
            order = 4,
            args = {
                header = {
                    name = "Layout Presets",
                    type = "header",
                    order = 0,
                },
                description = {
                    name = "|cFFFFFFFFQuickly apply pre-configured layouts for different playstyles and preferences.\n\n|cFFFFFF00Warning:|r Applying a preset will overwrite your current layout!|r",
                    type = "description",
                    order = 1,
                },

                -- Preset Categories
                combatPresets = {
                    name = "Combat Layouts",
                    type = "group",
                    order = 2,
                    inline = true,
                    args = {
                        presetDPS = {
                            name = "DPS Layout",
                            desc = "Optimized for damage dealers - action bars centered, unit frames compact",
                            type = "execute",
                            order = 1,
                            confirm = true,
                            confirmText = "Apply DPS layout preset? This will overwrite your current layout!",
                            func = function()
                                -- Apply DPS-optimized layout
                                local UnitFrameModule = RUI:GetModule("UnitFrame")
                                local ActionBarModule = RUI:GetModule("ActionBar")

                                if UnitFrameModule then UnitFrameModule:LoadDefaultSettings() end
                                if ActionBarModule then ActionBarModule:LoadDefaultSettings() end

                                -- Compact unit frames for DPS
                                RUI.DB.profile.widgets.player.scale = 0.9
                                RUI.DB.profile.widgets.target.scale = 0.9
                                RUI.DB.profile.widgets.focus.scale = 0.8

                                -- Update all modules
                                if UnitFrameModule then UnitFrameModule:UpdateWidgets() end
                                if ActionBarModule then ActionBarModule:UpdateWidgets() end

                                print("|cFF00FF00RetailUI:|r DPS layout preset applied!")
                            end,
                        },
                        presetTank = {
                            name = "Tank Layout",
                            desc = "Optimized for tanks - larger unit frames, centered positioning",
                            type = "execute",
                            order = 2,
                            confirm = true,
                            confirmText = "Apply Tank layout preset? This will overwrite your current layout!",
                            func = function()
                                -- Apply Tank-optimized layout
                                local UnitFrameModule = RUI:GetModule("UnitFrame")
                                local ActionBarModule = RUI:GetModule("ActionBar")

                                if UnitFrameModule then UnitFrameModule:LoadDefaultSettings() end
                                if ActionBarModule then ActionBarModule:LoadDefaultSettings() end

                                -- Larger unit frames for tanks
                                RUI.DB.profile.widgets.player.scale = 1.2
                                RUI.DB.profile.widgets.target.scale = 1.2
                                RUI.DB.profile.widgets.focus.scale = 1.1

                                -- Update all modules
                                if UnitFrameModule then UnitFrameModule:UpdateWidgets() end
                                if ActionBarModule then ActionBarModule:UpdateWidgets() end

                                print("|cFF00FF00RetailUI:|r Tank layout preset applied!")
                            end,
                        },
                        presetHealer = {
                            name = "Healer Layout",
                            desc = "Optimized for healers - frames positioned for easy target switching",
                            type = "execute",
                            order = 3,
                            confirm = true,
                            confirmText = "Apply Healer layout preset? This will overwrite your current layout!",
                            func = function()
                                -- Apply Healer-optimized layout
                                local UnitFrameModule = RUI:GetModule("UnitFrame")
                                local ActionBarModule = RUI:GetModule("ActionBar")

                                if UnitFrameModule then UnitFrameModule:LoadDefaultSettings() end
                                if ActionBarModule then ActionBarModule:LoadDefaultSettings() end

                                -- Healer-friendly positioning
                                RUI.DB.profile.widgets.player = { anchor = "BOTTOMLEFT", posX = 50, posY = 200, scale = 1.0 }
                                RUI.DB.profile.widgets.target = { anchor = "BOTTOMLEFT", posX = 250, posY = 200, scale = 1.0 }
                                RUI.DB.profile.widgets.focus = { anchor = "BOTTOMLEFT", posX = 150, posY = 300, scale = 1.0 }

                                -- Update all modules
                                if UnitFrameModule then UnitFrameModule:UpdateWidgets() end
                                if ActionBarModule then ActionBarModule:UpdateWidgets() end

                                print("|cFF00FF00RetailUI:|r Healer layout preset applied!")
                            end,
                        },
                    },
                },

                screenPresets = {
                    name = "Screen Size Layouts",
                    type = "group",
                    order = 3,
                    inline = true,
                    args = {
                        preset1080p = {
                            name = "1080p Layout",
                            desc = "Optimized for 1920x1080 resolution",
                            type = "execute",
                            order = 1,
                            confirm = true,
                            confirmText = "Apply 1080p layout preset? This will overwrite your current layout!",
                            func = function()
                                -- Reset to defaults (designed for 1080p)
                                local modules = {
                                    RUI:GetModule("UnitFrame"),
                                    RUI:GetModule("ActionBar"),
                                    RUI:GetModule("CastingBar"),
                                    RUI:GetModule("Minimap"),
                                    RUI:GetModule("QuestTracker"),
                                    RUI:GetModule("BuffFrame")
                                }

                                for _, module in pairs(modules) do
                                    if module and module.LoadDefaultSettings then
                                        module:LoadDefaultSettings()
                                        if module.UpdateWidgets then
                                            module:UpdateWidgets()
                                        end
                                    end
                                end

                                print("|cFF00FF00RetailUI:|r 1080p layout preset applied!")
                            end,
                        },
                        preset1440p = {
                            name = "1440p Layout",
                            desc = "Optimized for 2560x1440 resolution - larger scales",
                            type = "execute",
                            order = 2,
                            confirm = true,
                            confirmText = "Apply 1440p layout preset? This will overwrite your current layout!",
                            func = function()
                                -- Apply 1440p scaling
                                local modules = {
                                    RUI:GetModule("UnitFrame"),
                                    RUI:GetModule("ActionBar"),
                                    RUI:GetModule("CastingBar"),
                                    RUI:GetModule("Minimap"),
                                    RUI:GetModule("QuestTracker"),
                                    RUI:GetModule("BuffFrame")
                                }

                                for _, module in pairs(modules) do
                                    if module and module.LoadDefaultSettings then
                                        module:LoadDefaultSettings()
                                        if module.UpdateWidgets then
                                            module:UpdateWidgets()
                                        end
                                    end
                                end

                                -- Scale up for 1440p
                                local scaleMultiplier = 1.3
                                for frameName, _ in pairs(RUI.DB.profile.widgets) do
                                    if RUI.DB.profile.widgets[frameName].scale then
                                        RUI.DB.profile.widgets[frameName].scale = RUI.DB.profile.widgets[frameName].scale * scaleMultiplier
                                    else
                                        RUI.DB.profile.widgets[frameName].scale = scaleMultiplier
                                    end
                                end

                                -- Update all modules
                                for _, module in pairs(modules) do
                                    if module and module.UpdateWidgets then
                                        module:UpdateWidgets()
                                    end
                                end

                                print("|cFF00FF00RetailUI:|r 1440p layout preset applied!")
                            end,
                        },
                    },
                },
            },
        },

        reset = {
            name = "🔄 Reset & Tools",
            type = "group",
            order = 5,
            args = {
                header = {
                    name = "Reset Options and Utilities",
                    type = "header",
                    order = 0,
                },
                description = {
                    name = "|cFFFFFFFFReset settings, backup configurations, and access diagnostic tools.\n\n|cFFFF0000Warning:|r Reset operations cannot be undone!|r",
                    type = "description",
                    order = 1,
                },

                -- Reset Options
                resetOptions = {
                    name = "Reset Options",
                    type = "group",
                    order = 2,
                    inline = true,
                    args = {
                        resetAll = {
                            name = "Reset Everything",
                            desc = "Reset ALL settings to defaults - frames, grid, colors, everything",
                            type = 'execute',
                            order = 1,
                            confirm = true,
                            confirmText = "Are you sure you want to reset EVERYTHING to defaults? This cannot be undone!",
                            func = function()
                                -- Reset all modules
                                local modules = {
                                    RUI:GetModule("UnitFrame"),
                                    RUI:GetModule("ActionBar"),
                                    RUI:GetModule("CastingBar"),
                                    RUI:GetModule("Minimap"),
                                    RUI:GetModule("QuestTracker"),
                                    RUI:GetModule("BuffFrame")
                                }

                                for _, module in pairs(modules) do
                                    if module and module.LoadDefaultSettings then
                                        module:LoadDefaultSettings()
                                        if module.UpdateWidgets then
                                            module:UpdateWidgets()
                                        end
                                    end
                                end

                                -- Reset all addon settings
                                RUI.DB.profile.gridSize = 32
                                RUI.DB.profile.gridAlpha = 0.4
                                RUI.DB.profile.showCenterDots = true
                                RUI.DB.profile.gridColor = {1, 1, 1}
                                RUI.DB.profile.enableSnapping = true
                                RUI.DB.profile.snapTolerance = 16
                                RUI.DB.profile.centerBasedSnapping = true
                                RUI.DB.profile.centerDotSize = 6
                                RUI.DB.profile.centerDotColor = {1, 0, 0, 0.9}

                                print("|cFF00FF00RetailUI:|r Everything reset to defaults!")
                            end,
                        },
                        resetFrames = {
                            name = "Reset Frame Positions",
                            desc = "Reset all frame positions and scales to defaults",
                            type = 'execute',
                            order = 2,
                            confirm = true,
                            confirmText = "Reset all frame positions to defaults?",
                            func = function()
                                local modules = {
                                    RUI:GetModule("UnitFrame"),
                                    RUI:GetModule("ActionBar"),
                                    RUI:GetModule("CastingBar"),
                                    RUI:GetModule("Minimap"),
                                    RUI:GetModule("QuestTracker"),
                                    RUI:GetModule("BuffFrame")
                                }

                                for _, module in pairs(modules) do
                                    if module and module.LoadDefaultSettings then
                                        module:LoadDefaultSettings()
                                        if module.UpdateWidgets then
                                            module:UpdateWidgets()
                                        end
                                    end
                                end

                                print("|cFF00FF00RetailUI:|r All frame positions reset to defaults!")
                            end,
                        },
                        resetGrid = {
                            name = "Reset Grid Settings",
                            desc = "Reset grid size, transparency, and colors to defaults",
                            type = 'execute',
                            order = 3,
                            func = function()
                                RUI.DB.profile.gridSize = 32
                                RUI.DB.profile.gridAlpha = 0.4
                                RUI.DB.profile.gridColor = {1, 1, 1}
                                RUI.DB.profile.showCenterDots = true
                                RUI.DB.profile.centerDotSize = 6
                                RUI.DB.profile.centerDotColor = {1, 0, 0, 0.9}

                                -- Refresh grid if active
                                local EditorMode = RUI:GetModule('EditorMode')
                                if EditorMode and EditorMode:IsShown() then
                                    EditorMode:RefreshGrid()
                                    if RUI.DB.profile.showCenterDots then
                                        RUI:HideCenterDots()
                                        RUI:ShowCenterDots()
                                    end
                                end

                                print("|cFF00FF00RetailUI:|r Grid settings reset to defaults!")
                            end,
                        },
                    },
                },

                -- Diagnostic Tools
                diagnosticTools = {
                    name = "Diagnostic Tools",
                    type = "group",
                    order = 3,
                    inline = true,
                    args = {
                        showFrameInfo = {
                            name = "Show Frame Information",
                            desc = "Display detailed information about all configured frames",
                            type = 'execute',
                            order = 1,
                            func = function()
                                print("|cFF00FF00RetailUI Frame Information:|r")
                                print("------------------------")

                                local frameCount = 0
                                for frameName, widget in pairs(RUI.DB.profile.widgets) do
                                    frameCount = frameCount + 1
                                    local anchor = widget.anchor or "CENTER"
                                    local posX = widget.posX or 0
                                    local posY = widget.posY or 0
                                    local scale = widget.scale or 1.0

                                    print(string.format("|cFFFFFFFF%s:|r Anchor=%s, X=%.0f, Y=%.0f, Scale=%.2f",
                                        frameName, anchor, posX, posY, scale))
                                end

                                print("------------------------")
                                print(string.format("|cFF00FF00Total Frames:|r %d", frameCount))
                                print(string.format("|cFF00FF00Grid Size:|r %dpx", RUI.DB.profile.gridSize or 32))
                                print(string.format("|cFF00FF00Grid Alpha:|r %.1f", RUI.DB.profile.gridAlpha or 0.4))
                            end,
                        },
                        exportSettings = {
                            name = "Export Settings",
                            desc = "Export current settings to chat for sharing or backup",
                            type = 'execute',
                            order = 2,
                            func = function()
                                print("|cFF00FF00RetailUI Settings Export:|r")
                                print("Copy the following line to backup your settings:")
                                print("------------------------")

                                -- Create a simplified export string
                                local exportData = {
                                    gridSize = RUI.DB.profile.gridSize,
                                    gridAlpha = RUI.DB.profile.gridAlpha,
                                    showCenterDots = RUI.DB.profile.showCenterDots,
                                    frameCount = 0
                                }

                                for _ in pairs(RUI.DB.profile.widgets) do
                                    exportData.frameCount = exportData.frameCount + 1
                                end

                                print(string.format("RetailUI_Export: Grid=%d,Alpha=%.1f,Dots=%s,Frames=%d",
                                    exportData.gridSize or 32,
                                    exportData.gridAlpha or 0.4,
                                    exportData.showCenterDots and "true" or "false",
                                    exportData.frameCount))
                                print("------------------------")
                                print("|cFFFFFF00Note:|r This is a basic export. Full settings backup requires addon data folder backup.")
                            end,
                        },
                        reloadUI = {
                            name = "Reload UI",
                            desc = "Reload the user interface to apply all changes",
                            type = 'execute',
                            order = 3,
                            func = function()
                                print("|cFF00FF00RetailUI:|r Reloading UI...")
                                ReloadUI()
                            end,
                        },
                    },
                },

                -- Minimap Button
                minimapButton = {
                    name = "Minimap Button",
                    type = "group",
                    order = 4,
                    inline = true,
                    args = {
                        showMinimapButton = {
                            name = "Show Minimap Button",
                            desc = "Show or hide the RetailUI minimap button",
                            type = "toggle",
                            order = 1,
                            get = function() return not RUI.DB.profile.minimap.hide end,
                            set = function(info, value)
                                RUI.DB.profile.minimap.hide = not value
                                RUI:ToggleMinimapButton()
                            end,
                        },
                        minimapButtonInfo = {
                            name = "Minimap Button Help",
                            type = "description",
                            order = 2,
                            fontSize = "medium",
                            get = function()
                                return "|cFFFFFFFF• Left-click: Open settings\n• Right-click: Toggle edit mode|r"
                            end,
                        },
                    },
                },
            }
        },
    }
}

-- Simplified slash command options (for backward compatibility)
RUI.optionsSlash = {
    name = "RetailUI Commands",
    order = 0,
    type = "group",
    args = {
        edit = {
            name = "Enable Edit Mode",
            type = 'execute',
            order = 0,
            func = function()
                local EditorMode = RUI:GetModule('EditorMode')
                if EditorMode:IsShown() then
                    EditorMode:Hide()
                else
                    EditorMode:Show()
                end
            end,
            dialogHidden = true
        },
        config = {
            name = "Open Settings",
            type = 'execute',
            order = 1,
            func = function()
                AceConfigDialog:Open("RetailUI")
            end,
            dialogHidden = true
        },
        settings = {
            name = "Settings",
            type = 'execute',
            order = 2,
            func = function()
                AceConfigDialog:Open("RetailUI")
            end,
            dialogHidden = true
        },
    }
}

RUI.default = {
    profile = {
        widgets = {},

        -- Grid and Snapping Settings
        gridSize = RUI.Constants.GRID.DEFAULT_SIZE,
        gridAlpha = RUI.Constants.GRID.DEFAULT_ALPHA,
        gridColor = RUI.Constants.GRID.DEFAULT_COLOR,
        enableSnapping = true,
        snapTolerance = RUI.Constants.GRID.DEFAULT_SNAP_TOLERANCE,
        centerBasedSnapping = true,

        -- Center Dots Settings
        showCenterDots = true,
        centerDotSize = RUI.Constants.CENTER_DOTS.DEFAULT_SIZE,
        centerDotColor = RUI.Constants.CENTER_DOTS.DEFAULT_COLOR,

        -- Minimap Button Settings
        minimap = {
            hide = false,
        },

        -- Editor Mode Settings
        editorMode = {
            showHelp = true,
            autoHideInCombat = true,
        },

        -- Visual Settings
        theme = "default",
        uiScale = 1.0,

        -- Advanced Settings
        debugMode = false,
        autoSave = true,
        backupCount = 5,
    }
}

-- Minimap button configuration
local LibDBIcon = LibStub("LibDBIcon-1.0")
local minimapLDB = LibStub("LibDataBroker-1.1"):NewDataObject("RetailUI", {
    type = "launcher",
    text = "RetailUI",
    icon = "Interface\\Icons\\INV_Misc_Gear_01",
    OnClick = function(self, button)
        if button == "LeftButton" then
            AceConfigDialog:Open("RetailUI")
        elseif button == "RightButton" then
            local EditorMode = RUI:GetModule('EditorMode')
            if EditorMode:IsShown() then
                EditorMode:Hide()
            else
                EditorMode:Show()
            end
        end
    end,
    OnTooltipShow = function(tooltip)
        tooltip:AddLine("RetailUI")
        tooltip:AddLine("|cFFFFFFFFLeft-click|r to open settings")
        tooltip:AddLine("|cFFFFFFFFRight-click|r to toggle edit mode")
    end,
})

-- Function to initialize minimap button
function RUI:InitializeMinimapButton()
    LibDBIcon:Register("RetailUI", minimapLDB, self.DB.profile.minimap)
end

-- Function to toggle minimap button
function RUI:ToggleMinimapButton()
    self.DB.profile.minimap.hide = not self.DB.profile.minimap.hide
    if self.DB.profile.minimap.hide then
        LibDBIcon:Hide("RetailUI")
    else
        LibDBIcon:Show("RetailUI")
    end
end
