--[[
    Copyright (c) Dmitriy. All rights reserved.
    Licensed under the MIT license. See LICENSE file in the project root for details.
]]

-- Constants and configuration values for RetailUI
-- This centralizes all magic numbers and commonly used values

RUI.Constants = {
    -- Frame Sizes (width, height in pixels)
    FRAME_SIZES = {
        PLAYER_PORTRAIT = {width = 56, height = 56},
        HEALTH_BAR = {width = 123, height = 20},
        MANA_BAR = {width = 123, height = 9},
        PLAYER_FRAME = {width = 192, height = 68},
        TARGET_FRAME = {width = 192, height = 68},
        FOCUS_FRAME = {width = 192, height = 68},
        PET_FRAME = {width = 120, height = 47},
        TOT_FRAME = {width = 120, height = 47},
        BOSS_FRAME = {width = 192, height = 68},
        MINIMAP_FRAME = {width = 230, height = 230},
        MINIMAP_BORDER = {width = 232, height = 232},
        QUEST_LOG_FRAME = {width = 230, height = 500},
        QUEST_TRACKER_FRAME = {width = 230, height = 500},
        CASTING_BAR_FRAME = {width = 228, height = 18},
        REP_EXP_BAR = {height = 10},
    },
    
    -- Texture Coordinates for Runes (left, right, top, bottom)
    TEXTURE_COORDS = {
        RUNE_BLOOD = {0/128, 34/128, 0/128, 34/128},
        RUNE_UNHOLY = {0/128, 34/128, 68/128, 102/128},
        RUNE_FROST = {34/128, 68/128, 0/128, 34/128},
        RUNE_DEATH = {68/128, 102/128, 0/128, 34/128},
        
        -- Action Bar Textures
        ACTION_BAR_HORIZONTAL = {0, 512/512, 14/2048, 85/2048},
    },
    
    -- Event and Update Settings
    EVENTS = {
        THROTTLE_DELAY = 0.1,
        UPDATE_FREQUENCY = 0.05,
        PERFORMANCE_UPDATE_INTERVAL = 10,
        POSITION_UPDATE_DELAY = 0.1,
    },
    
    -- Grid and Snapping Settings
    GRID = {
        DEFAULT_SIZE = 32,
        DEFAULT_ALPHA = 0.4,
        DEFAULT_COLOR = {1, 1, 1}, -- White
        DEFAULT_SNAP_TOLERANCE = 16,
    },
    
    -- Center Dots Settings
    CENTER_DOTS = {
        DEFAULT_SIZE = 6,
        DEFAULT_COLOR = {1, 0, 0, 0.9}, -- Red with 90% opacity
        DRAW_LAYER = 7,
    },
    
    -- Frame Levels and Strata
    FRAME_LEVELS = {
        UI_FRAME = 100,
        EDITOR_OVERLAY = 7,
    },
    
    FRAME_STRATA = {
        UI_FRAME = 'FULLSCREEN',
        DIALOG = 'FULLSCREEN_DIALOG',
    },
    
    -- Performance Thresholds
    PERFORMANCE = {
        LOW_LATENCY = 300,
        MEDIUM_LATENCY = 600,
    },
    
    -- Validation Limits
    VALIDATION = {
        MAX_SCALE = 5.0,
        MIN_SCALE = 0.1,
        MAX_FRAME_NAME_LENGTH = 50,
        FRAME_NAME_PATTERN = "^[%w_]+$",
    },
    
    -- Color Definitions
    COLORS = {
        -- Health bar colors by reaction
        REACTION = {
            [1] = {r = 1.0, g = 0.0, b = 0.0}, -- Hostile (Red)
            [2] = {r = 1.0, g = 0.0, b = 0.0}, -- Hostile (Red)
            [3] = {r = 1.0, g = 0.5, b = 0.0}, -- Unfriendly (Orange)
            [4] = {r = 1.0, g = 1.0, b = 0.0}, -- Neutral (Yellow)
            [5] = {r = 0.0, g = 1.0, b = 0.0}, -- Friendly (Green)
            [6] = {r = 0.0, g = 1.0, b = 0.0}, -- Friendly (Green)
            [7] = {r = 0.0, g = 1.0, b = 0.0}, -- Friendly (Green)
            [8] = {r = 0.0, g = 1.0, b = 0.0}, -- Friendly (Green)
        },
        
        -- Class colors
        CLASS = {
            WARRIOR = {r = 0.78, g = 0.61, b = 0.43},
            PALADIN = {r = 0.96, g = 0.55, b = 0.73},
            HUNTER = {r = 0.67, g = 0.83, b = 0.45},
            ROGUE = {r = 1.00, g = 0.96, b = 0.41},
            PRIEST = {r = 1.00, g = 1.00, b = 1.00},
            DEATHKNIGHT = {r = 0.77, g = 0.12, b = 0.23},
            SHAMAN = {r = 0.14, g = 0.35, b = 1.00},
            MAGE = {r = 0.25, g = 0.78, b = 0.92},
            WARLOCK = {r = 0.53, g = 0.53, b = 0.93},
            DRUID = {r = 1.00, g = 0.49, b = 0.04},
        },
        
        -- Default colors
        DEFAULT_HEALTH = {r = 0.48, g = 0.86, b = 0.15}, -- Green
        DEFAULT_MANA = {r = 0.0, g = 0.0, b = 1.0}, -- Blue
        
        -- Log colors
        LOG = {
            ERROR = "|cFFFF0000",
            WARN = "|cFFFFFF00",
            INFO = "|cFF00FF00",
            DEBUG = "|cFFCCCCCC",
            RESET = "|r",
        },
    },
    
    -- Texture Paths
    TEXTURES = {
        ACTION_BAR_HORIZONTAL = "Interface\\AddOns\\RetailUI\\Textures\\UI\\ActionBarHorizontal.blp",
        DEATH_KNIGHT_RUNES = "Interface\\AddOns\\RetailUI\\Textures\\PlayerFrame\\ClassOverlayDeathKnightRunes.BLP",
    },
    
    -- Action Bar Constants
    ACTION_BARS = {
        MAIN_ACTION_BAR_ID = 1,
        BONUS_ACTION_BAR_ID = 12,
        PET_ACTION_BAR_ID = 13,
        POSSESS_ACTION_BAR_ID = 14,
        MULTICAST_ACTION_BAR_ID = 15,
        VEHICLE_ACTION_BAR_ID = 16,
        
        BUTTON_SIZE = 42,
        BUTTON_SPACING = 4,
        BUTTONS_PER_ROW = 12,
    },
    
    -- Interface Version Constants
    INTERFACE_VERSIONS = {
        WOTLK_MIN = 30300,
        WOTLK_MAX = 39999,
        RETAIL_MIN = 90000,
    },
}

-- Convenience functions for accessing constants
function RUI:GetFrameSize(frameType)
    return self.Constants.FRAME_SIZES[frameType] or {width = 100, height = 100}
end

function RUI:GetTextureCoords(textureType)
    return self.Constants.TEXTURE_COORDS[textureType] or {0, 1, 0, 1}
end

function RUI:GetReactionColor(reaction)
    return self.Constants.COLORS.REACTION[reaction] or self.Constants.COLORS.DEFAULT_HEALTH
end

function RUI:GetClassColor(class)
    return self.Constants.COLORS.CLASS[class] or {r = 1, g = 1, b = 1}
end
