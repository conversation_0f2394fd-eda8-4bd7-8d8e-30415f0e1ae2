# RetailUI Developer Guide

## Getting Started

### Prerequisites

- World of Warcraft: Wrath of the Lich King 3.3.5
- Basic knowledge of Lua programming
- Understanding of WoW addon development
- Familiarity with Ace3 libraries

### Development Environment Setup

1. **Clone/Download RetailUI**
   ```
   Interface/AddOns/RetailUI/
   ```

2. **Enable Debug Mode**
   ```lua
   /rui
   -- Enable "Debug Mode" in settings
   ```

3. **Development Tools**
   - `/ruitest` - Run test suite
   - `/ruisecurity performance` - Performance metrics
   - `/ruisecurity memory` - Memory usage
   - `/ruisecurity audit` - Security audit log

## Architecture Overview

### Core Components

```
RetailUI/
├── Core.lua              # Main addon core
├── Constants.lua         # Centralized constants
├── ModuleBase.lua        # Module architecture
├── Config.lua           # Settings system
├── Tests.lua            # Testing framework
└── Modules/             # Individual modules
    ├── UnitFrame.lua    # Unit frame management
    ├── ActionBar.lua    # Action bar system
    ├── CastingBar.lua   # Casting bars
    └── ...
```

### Design Principles

1. **Modularity** - Each feature is a separate module
2. **Security** - All inputs validated and sanitized
3. **Performance** - Throttled events and optimized updates
4. **Compatibility** - WotLK 3.3.5 compatible APIs only
5. **Maintainability** - Consistent patterns and documentation

## Module Development

### Creating a New Module

1. **Create Module File**
   ```lua
   -- Modules/MyModule.lua
   local RUI = LibStub('AceAddon-3.0'):GetAddon('RetailUI')
   local Module = RUI:NewModule('MyModule', 'AceEvent-3.0', 'AceHook-3.0')
   
   -- Inherit from ModuleBase
   for key, value in pairs(RUI.ModuleBase) do
       if not Module[key] then
           Module[key] = value
       end
   end
   
   -- Module metadata
   Module.moduleName = "MyModule"
   Module.moduleVersion = "1.0.0"
   Module.moduleDescription = "My custom module"
   Module.dependencies = {}
   Module.optionalDependencies = {}
   ```

2. **Add to TOC File**
   ```
   Modules/MyModule.lua
   ```

3. **Implement Lifecycle Methods**
   ```lua
   function Module:OnInitialize()
       -- One-time setup
   end
   
   function Module:OnEnable()
       -- Register events, create frames
       self:RegisterEvent("PLAYER_ENTERING_WORLD")
       self.myFrame = self:CreateUIFrame(200, 100, "MyFrame")
   end
   
   function Module:OnDisable()
       -- Cleanup handled automatically
   end
   ```

### Module Best Practices

#### 1. Use Standardized Frame Creation
```lua
-- Good
self.frame = self:CreateUIFrame(width, height, "FrameName")

-- Avoid
self.frame = CreateFrame("Frame", "FrameName", UIParent)
```

#### 2. Register Events Safely
```lua
-- Good
self:RegisterEvent("UNIT_HEALTH")

-- Avoid
self:RegisterEvent("UNIT_HEALTH")
-- Manual cleanup required
```

#### 3. Use Performance Monitoring
```lua
function Module:ExpensiveOperation()
    RUI.Performance:StartTimer("MyModule_ExpensiveOp")
    
    -- Your code here
    
    RUI.Performance:EndTimer("MyModule_ExpensiveOp")
end
```

#### 4. Implement Error Handling
```lua
function Module:SafeOperation()
    local success, result = pcall(function()
        -- Potentially failing operation
        return self:DoSomething()
    end)
    
    if not success then
        RUI.Logger:Error("Operation failed: %s", result)
        return nil
    end
    
    return result
end
```

### Frame Management

#### Creating Frames
```lua
-- Standard UI frame
local frame = self:CreateUIFrame(200, 100, "MyFrame")

-- Custom frame with specific parent
local customFrame = self:CreateFrame("Frame", "MyCustomFrame", UIParent)

-- Button with template
local button = self:CreateFrame("Button", "MyButton", frame, "UIPanelButtonTemplate")
```

#### Frame Positioning
```lua
-- Use constants for consistent positioning
frame:SetPoint("CENTER", UIParent, "CENTER", 0, 0)

-- Save position for editor mode
RUI:SaveUIFramePosition(frame, "myFrameWidget")

-- Load saved position
local widgets = {"myFrameWidget"}
RUI:CheckSettingsExists(self, widgets)
```

### Event Handling

#### Throttled Events
```lua
function Module:UNIT_HEALTH_FREQUENT(event, unit)
    -- Throttle frequent updates
    RUI.Throttle:ThrottledCall("health_" .. unit, 0.1, function()
        self:UpdateHealthBar(unit)
    end)
end
```

#### Event Tracking
```lua
function Module:PLAYER_TARGET_CHANGED()
    RUI.Throttle:TrackEvent("PLAYER_TARGET_CHANGED")
    -- Your event handling code
end
```

### Security Considerations

#### Input Validation
```lua
function Module:HandleUserInput(input)
    -- Sanitize input
    local safeInput = RUI.Security:SanitizeInput(input, "string")
    if not safeInput then
        RUI.Logger:Error("Invalid input provided")
        return false
    end
    
    -- Validate further if needed
    local valid, result = RUI.Validator:ValidateFrameName(safeInput)
    if not valid then
        RUI.Logger:Error("Validation failed: %s", result)
        return false
    end
    
    return true
end
```

#### Secure Storage
```lua
function Module:SaveSetting(key, value)
    if RUI.Security:SecureStore(key, value, self.moduleName) then
        RUI.Logger:Info("Setting saved: %s", key)
        return true
    else
        RUI.Logger:Error("Failed to save setting: %s", key)
        return false
    end
end
```

## Testing

### Unit Testing

#### Writing Tests
```lua
-- Add to Tests.lua
TestMyModule = function(self)
    self:RunTest("MyModule Functionality", function()
        local module = RUI.SafeOps:GetModuleSafely("MyModule")
        assert(module ~= nil, "MyModule not found")
        
        -- Test module functionality
        local result = module:DoSomething()
        assert(result == expected, "Unexpected result")
    end)
end
```

#### Running Tests
```lua
-- Run all tests
/ruitest

-- Check specific functionality
local module = RUI.SafeOps:GetModuleSafely("MyModule")
local status = module:GetStatus()
print("Module status:", status.enabled)
```

### Performance Testing

#### Benchmarking
```lua
function Module:BenchmarkOperation()
    local iterations = 1000
    
    RUI.Performance:StartTimer("BenchmarkTest")
    
    for i = 1, iterations do
        self:MyOperation()
    end
    
    RUI.Performance:EndTimer("BenchmarkTest")
    
    -- Check results
    RUI.Performance:PrintReport()
end
```

### Integration Testing

#### Module Dependencies
```lua
function Module:TestDependencies()
    for _, depName in pairs(self.dependencies) do
        local dep = RUI.SafeOps:GetModuleSafely(depName)
        if not dep then
            RUI.Logger:Error("Missing dependency: %s", depName)
            return false
        end
        
        if not dep.isEnabled then
            RUI.Logger:Error("Dependency not enabled: %s", depName)
            return false
        end
    end
    
    return true
end
```

## Debugging

### Logging
```lua
-- Use appropriate log levels
RUI.Logger:Debug("Detailed debug info: %s", debugData)
RUI.Logger:Info("General information: %s", info)
RUI.Logger:Warn("Warning about: %s", warning)
RUI.Logger:Error("Error occurred: %s", error)
```

### Performance Debugging
```lua
-- Monitor specific operations
RUI.Performance:StartTimer("SuspiciousOperation")
self:SuspiciousOperation()
RUI.Performance:EndTimer("SuspiciousOperation")

-- Check system performance
local metrics = RUI.Performance:GetSystemMetrics()
if metrics.frameRate < 30 then
    RUI.Logger:Warn("Low FPS detected: %.1f", metrics.frameRate)
end
```

### Memory Debugging
```lua
-- Check memory usage
RUI.Memory:PrintMemoryReport()

-- Force cleanup
RUI.Memory:FullCleanup()

-- Check for leaks
local report = RUI.Memory:GetMemoryReport()
if report.totalFrames > 100 then
    RUI.Logger:Warn("High frame count: %d", report.totalFrames)
end
```

## Common Patterns

### Singleton Pattern
```lua
local MyManager = {
    instance = nil,
    
    GetInstance = function(self)
        if not self.instance then
            self.instance = {}
            -- Initialize instance
        end
        return self.instance
    end
}
```

### Observer Pattern
```lua
local EventDispatcher = {
    listeners = {},
    
    AddListener = function(self, event, callback)
        if not self.listeners[event] then
            self.listeners[event] = {}
        end
        table.insert(self.listeners[event], callback)
    end,
    
    FireEvent = function(self, event, ...)
        if self.listeners[event] then
            for _, callback in pairs(self.listeners[event]) do
                callback(...)
            end
        end
    end
}
```

### Factory Pattern
```lua
local FrameFactory = {
    CreateHealthBar = function(self, parent, unit)
        local frame = CreateFrame("StatusBar", nil, parent)
        frame.unit = unit
        -- Configure health bar
        return frame
    end,
    
    CreateManaBar = function(self, parent, unit)
        local frame = CreateFrame("StatusBar", nil, parent)
        frame.unit = unit
        -- Configure mana bar
        return frame
    end
}
```

## Troubleshooting

### Common Issues

1. **Module Not Loading**
   - Check TOC file includes module
   - Verify syntax errors with `/console scriptErrors 1`
   - Check dependencies are available

2. **Performance Issues**
   - Use `/ruisecurity performance` to identify bottlenecks
   - Check for unthrottled OnUpdate scripts
   - Monitor event frequency

3. **Memory Leaks**
   - Use `/ruisecurity memory` to check usage
   - Ensure frames are properly cleaned up
   - Check for circular references

4. **WotLK Compatibility**
   - Avoid modern API calls
   - Use RUI.Compat for compatibility layers
   - Test on actual WotLK client

### Getting Help

1. **Check Logs**
   ```lua
   /ruisecurity audit
   ```

2. **Run Diagnostics**
   ```lua
   /ruitest
   /ruisecurity report
   ```

3. **Enable Debug Mode**
   - More detailed logging
   - Automatic test execution
   - Performance monitoring

For more information, see the [API Documentation](API_Documentation.md).
