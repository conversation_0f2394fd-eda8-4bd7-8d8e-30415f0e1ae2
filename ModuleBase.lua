--[[
    Copyright (c) Dmitriy. All rights reserved.
    Licensed under the MIT license. See LICENSE file in the project root for details.
]]

-- Standardized module base class for RetailUI
-- Provides consistent initialization, error handling, and lifecycle management

RUI.ModuleBase = {
    -- Module metadata
    moduleName = nil,
    moduleVersion = "1.0.0",
    moduleDescription = nil,
    moduleAuthor = "RetailUI",
    
    -- Module state
    isInitialized = false,
    isEnabled = false,
    hasErrors = false,
    
    -- Module dependencies
    dependencies = {},
    optionalDependencies = {},
    
    -- Module frames and resources
    frames = {},
    events = {},
    hooks = {},
    timers = {},
    
    -- Initialization lifecycle
    Initialize = function(self)
        if self.isInitialized then
            RUI.Logger:Warn("Module %s already initialized", self.moduleName or "unknown")
            return false
        end
        
        RUI.Performance:StartTimer("ModuleInit_" .. (self.moduleName or "unknown"))
        
        -- Validate dependencies
        if not self:ValidateDependencies() then
            self.hasErrors = true
            return false
        end
        
        -- Register with memory management
        RUI.Memory:RegisterModule(self, self.moduleName)
        
        -- Call module-specific initialization
        local success, error = pcall(function()
            if self.OnInitialize then
                self:OnInitialize()
            end
        end)
        
        if not success then
            RUI.Logger:Error("Module %s initialization failed: %s", self.moduleName or "unknown", error)
            self.hasErrors = true
            return false
        end
        
        self.isInitialized = true
        RUI.Performance:EndTimer("ModuleInit_" .. (self.moduleName or "unknown"))
        RUI.Logger:Info("Module %s initialized successfully", self.moduleName or "unknown")
        
        return true
    end,
    
    -- Enable lifecycle
    Enable = function(self)
        if not self.isInitialized then
            RUI.Logger:Error("Cannot enable uninitialized module %s", self.moduleName or "unknown")
            return false
        end
        
        if self.isEnabled then
            RUI.Logger:Warn("Module %s already enabled", self.moduleName or "unknown")
            return true
        end
        
        RUI.Performance:StartTimer("ModuleEnable_" .. (self.moduleName or "unknown"))
        
        -- Call module-specific enable
        local success, error = pcall(function()
            if self.OnEnable then
                self:OnEnable()
            end
        end)
        
        if not success then
            RUI.Logger:Error("Module %s enable failed: %s", self.moduleName or "unknown", error)
            self.hasErrors = true
            return false
        end
        
        self.isEnabled = true
        RUI.Performance:EndTimer("ModuleEnable_" .. (self.moduleName or "unknown"))
        RUI.Logger:Debug("Module %s enabled", self.moduleName or "unknown")
        
        return true
    end,
    
    -- Disable lifecycle
    Disable = function(self)
        if not self.isEnabled then
            return true
        end
        
        RUI.Performance:StartTimer("ModuleDisable_" .. (self.moduleName or "unknown"))
        
        -- Cleanup resources
        self:CleanupResources()
        
        -- Call module-specific disable
        local success, error = pcall(function()
            if self.OnDisable then
                self:OnDisable()
            end
        end)
        
        if not success then
            RUI.Logger:Error("Module %s disable failed: %s", self.moduleName or "unknown", error)
        end
        
        self.isEnabled = false
        RUI.Performance:EndTimer("ModuleDisable_" .. (self.moduleName or "unknown"))
        RUI.Logger:Debug("Module %s disabled", self.moduleName or "unknown")
        
        return true
    end,
    
    -- Validate module dependencies
    ValidateDependencies = function(self)
        -- Check required dependencies
        for _, depName in pairs(self.dependencies) do
            local dep = RUI.SafeOps:GetModuleSafely(depName)
            if not dep then
                RUI.Logger:Error("Module %s missing required dependency: %s", self.moduleName or "unknown", depName)
                return false
            end
        end
        
        -- Check optional dependencies (warn if missing)
        for _, depName in pairs(self.optionalDependencies) do
            local dep = RUI.SafeOps:GetModuleSafely(depName)
            if not dep then
                RUI.Logger:Warn("Module %s missing optional dependency: %s", self.moduleName or "unknown", depName)
            end
        end
        
        return true
    end,
    
    -- Safe frame creation with automatic registration
    CreateFrame = function(self, frameType, name, parent, template)
        local frame = CreateFrame(frameType, name, parent, template)
        if frame then
            self.frames[frame] = {
                name = name,
                created = GetTime(),
                frameType = frameType
            }
            
            -- Register with memory management
            RUI.Memory:RegisterFrame(frame, self.moduleName, frameType)
        end
        return frame
    end,
    
    -- Safe UI frame creation
    CreateUIFrame = function(self, width, height, frameName)
        local frame = RUI:CreateUIFrame(width, height, frameName)
        if frame then
            self.frames[frame] = {
                name = frameName,
                created = GetTime(),
                frameType = "UIFrame"
            }
        end
        return frame
    end,
    
    -- Safe event registration
    RegisterEvent = function(self, event, handler)
        if not self.events[event] then
            self.events[event] = {
                handler = handler or self[event],
                registered = GetTime()
            }
            
            -- Register with actual event system
            if self.RegisterEvent then
                pcall(self.RegisterEvent, self, event)
            end
            
            RUI.Logger:Debug("Module %s registered event: %s", self.moduleName or "unknown", event)
        end
    end,
    
    -- Safe event unregistration
    UnregisterEvent = function(self, event)
        if self.events[event] then
            self.events[event] = nil
            
            -- Unregister with actual event system
            if self.UnregisterEvent then
                pcall(self.UnregisterEvent, self, event)
            end
            
            RUI.Logger:Debug("Module %s unregistered event: %s", self.moduleName or "unknown", event)
        end
    end,
    
    -- Safe hook registration
    RegisterHook = function(self, object, method, handler)
        local hookKey = tostring(object) .. ":" .. method
        if not self.hooks[hookKey] then
            self.hooks[hookKey] = {
                object = object,
                method = method,
                handler = handler,
                registered = GetTime()
            }
            
            -- Register with memory management
            RUI.Memory:RegisterHook({
                object = self,
                method = method
            })
            
            RUI.Logger:Debug("Module %s registered hook: %s", self.moduleName or "unknown", hookKey)
        end
    end,
    
    -- Timer management
    CreateTimer = function(self, key, delay, callback, repeating)
        if self.timers[key] then
            self:CancelTimer(key)
        end
        
        local timer = {
            key = key,
            delay = delay,
            callback = callback,
            repeating = repeating or false,
            created = GetTime(),
            frame = CreateFrame("Frame")
        }
        
        local elapsed = 0
        timer.frame:SetScript("OnUpdate", function(self, dt)
            elapsed = elapsed + dt
            if elapsed >= delay then
                if repeating then
                    elapsed = 0
                else
                    timer.frame:SetScript("OnUpdate", nil)
                end
                callback()
            end
        end)
        
        self.timers[key] = timer
        RUI.Memory:RegisterTimer(self.moduleName .. "_" .. key, timer.frame)
        
        return timer
    end,
    
    -- Cancel timer
    CancelTimer = function(self, key)
        local timer = self.timers[key]
        if timer then
            if timer.frame then
                timer.frame:SetScript("OnUpdate", nil)
            end
            self.timers[key] = nil
        end
    end,
    
    -- Cleanup all module resources
    CleanupResources = function(self)
        -- Cleanup frames
        for frame, _ in pairs(self.frames) do
            RUI.Memory:UnregisterFrame(frame)
        end
        self.frames = {}
        
        -- Cleanup events
        for event, _ in pairs(self.events) do
            self:UnregisterEvent(event)
        end
        self.events = {}
        
        -- Cleanup timers
        for key, _ in pairs(self.timers) do
            self:CancelTimer(key)
        end
        self.timers = {}
        
        -- Cleanup hooks
        self.hooks = {}
        
        RUI.Logger:Debug("Cleaned up resources for module %s", self.moduleName or "unknown")
    end,
    
    -- Get module status
    GetStatus = function(self)
        return {
            name = self.moduleName,
            version = self.moduleVersion,
            description = self.moduleDescription,
            initialized = self.isInitialized,
            enabled = self.isEnabled,
            hasErrors = self.hasErrors,
            frameCount = self:GetFrameCount(),
            eventCount = self:GetEventCount(),
            timerCount = self:GetTimerCount()
        }
    end,
    
    -- Get frame count
    GetFrameCount = function(self)
        local count = 0
        for _ in pairs(self.frames) do
            count = count + 1
        end
        return count
    end,
    
    -- Get event count
    GetEventCount = function(self)
        local count = 0
        for _ in pairs(self.events) do
            count = count + 1
        end
        return count
    end,
    
    -- Get timer count
    GetTimerCount = function(self)
        local count = 0
        for _ in pairs(self.timers) do
            count = count + 1
        end
        return count
    end,
}

-- Create a new module instance
function RUI:CreateModule(moduleName, moduleData)
    -- Create module instance
    local module = {}
    
    -- Copy base functionality
    for key, value in pairs(self.ModuleBase) do
        module[key] = value
    end
    
    -- Copy module-specific data
    if moduleData then
        for key, value in pairs(moduleData) do
            module[key] = value
        end
    end
    
    -- Set module name
    module.moduleName = moduleName
    
    -- Initialize the module
    if not module:Initialize() then
        RUI.Logger:Error("Failed to create module: %s", moduleName)
        return nil
    end
    
    return module
end
