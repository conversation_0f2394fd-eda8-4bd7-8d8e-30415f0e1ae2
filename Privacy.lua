--[[
    Copyright (c) Dmitriy. All rights reserved.
    Licensed under the MIT license. See LICENSE file in the project root for details.
]]

-- Privacy controls and data governance system for RetailUI
-- Implements GDPR compliance, user consent, and data protection

RUI.Privacy = {
    -- Privacy settings and consent
    consent = {
        dataCollection = false,
        analytics = false,
        errorReporting = false,
        performanceMetrics = false,
        usageStatistics = false,
        consentVersion = "1.0",
        consentDate = nil,
        lastUpdated = nil
    },
    
    -- Data categories and their purposes
    dataCategories = {
        essential = {
            description = "Essential data required for addon functionality",
            purpose = "Core addon operation and settings storage",
            retention = "Until addon is uninstalled",
            required = true,
            data = {"UI positions", "scale settings", "enabled modules"}
        },
        functional = {
            description = "Data to enhance user experience",
            purpose = "Personalization and feature improvements",
            retention = "Until user opts out or 2 years",
            required = false,
            data = {"user preferences", "customization settings", "accessibility options"}
        },
        analytics = {
            description = "Anonymous usage data for improvement",
            purpose = "Understanding feature usage and performance",
            retention = "90 days",
            required = false,
            data = {"feature usage counts", "performance metrics", "error frequencies"}
        },
        diagnostic = {
            description = "Technical data for troubleshooting",
            purpose = "Debugging and technical support",
            retention = "30 days",
            required = false,
            data = {"error logs", "performance data", "system information"}
        }
    },
    
    -- Data processing activities
    processingActivities = {},
    
    -- Initialize privacy system
    Initialize = function(self)
        -- Load existing consent
        self:LoadConsent()
        
        -- Check if consent is needed
        if not self:HasValidConsent() then
            self:ShowConsentDialog()
        end
        
        -- Initialize data governance
        self:InitializeDataGovernance()
        
        RUI.Logger:Info("Privacy system initialized")
    end,
    
    -- Load consent from saved variables
    LoadConsent = function(self)
        if RUI.DB and RUI.DB.profile and RUI.DB.profile.privacy then
            for key, value in pairs(RUI.DB.profile.privacy) do
                if self.consent[key] ~= nil then
                    self.consent[key] = value
                end
            end
        end
    end,
    
    -- Save consent to saved variables
    SaveConsent = function(self)
        if not RUI.DB.profile.privacy then
            RUI.DB.profile.privacy = {}
        end
        
        for key, value in pairs(self.consent) do
            RUI.DB.profile.privacy[key] = value
        end
        
        self.consent.lastUpdated = time()
        RUI.DB.profile.privacy.lastUpdated = self.consent.lastUpdated
        
        RUI.Logger:Info("Privacy consent saved")
    end,
    
    -- Check if user has valid consent
    HasValidConsent = function(self)
        -- Check if consent exists and is current version
        return self.consent.consentDate ~= nil and 
               self.consent.consentVersion == "1.0"
    end,
    
    -- Show consent dialog
    ShowConsentDialog = function(self)
        RUI.Logger:Info("=== RetailUI Privacy Notice ===")
        RUI.Logger:Info("RetailUI respects your privacy and follows data protection principles.")
        RUI.Logger:Info("")
        RUI.Logger:Info("Data Categories:")
        
        for category, info in pairs(self.dataCategories) do
            local status = info.required and "REQUIRED" or "OPTIONAL"
            RUI.Logger:Info("  %s (%s): %s", category:upper(), status, info.description)
            RUI.Logger:Info("    Purpose: %s", info.purpose)
            RUI.Logger:Info("    Retention: %s", info.retention)
        end
        
        RUI.Logger:Info("")
        RUI.Logger:Info("Commands:")
        RUI.Logger:Info("  /ruiprivacy accept - Accept all data processing")
        RUI.Logger:Info("  /ruiprivacy minimal - Accept only essential data")
        RUI.Logger:Info("  /ruiprivacy custom - Customize data preferences")
        RUI.Logger:Info("  /ruiprivacy info - Show this information again")
        
        -- Set default minimal consent for essential functionality
        self:SetMinimalConsent()
    end,
    
    -- Set minimal consent (essential only)
    SetMinimalConsent = function(self)
        self.consent.dataCollection = true -- Essential only
        self.consent.analytics = false
        self.consent.errorReporting = false
        self.consent.performanceMetrics = false
        self.consent.usageStatistics = false
        self.consent.consentDate = time()
        self.consent.consentVersion = "1.0"
        
        self:SaveConsent()
        RUI.Logger:Info("Minimal consent set - only essential data will be processed")
    end,
    
    -- Set full consent
    SetFullConsent = function(self)
        self.consent.dataCollection = true
        self.consent.analytics = true
        self.consent.errorReporting = true
        self.consent.performanceMetrics = true
        self.consent.usageStatistics = true
        self.consent.consentDate = time()
        self.consent.consentVersion = "1.0"
        
        self:SaveConsent()
        RUI.Logger:Info("Full consent granted - all features enabled")
    end,
    
    -- Withdraw consent
    WithdrawConsent = function(self)
        self.consent.dataCollection = true -- Keep essential
        self.consent.analytics = false
        self.consent.errorReporting = false
        self.consent.performanceMetrics = false
        self.consent.usageStatistics = false
        self.consent.lastUpdated = time()
        
        self:SaveConsent()
        self:PurgeNonEssentialData()
        
        RUI.Logger:Info("Consent withdrawn - non-essential data purged")
    end,
    
    -- Initialize data governance
    InitializeDataGovernance = function(self)
        -- Set up data retention policies
        self:SetupDataRetention()
        
        -- Initialize processing activity log
        self.processingActivities = {}
        
        -- Set up automatic data cleanup
        self:ScheduleDataCleanup()
    end,
    
    -- Set up data retention policies
    SetupDataRetention = function(self)
        self.retentionPolicies = {
            auditLog = 90, -- days
            performanceMetrics = 30,
            errorLogs = 30,
            usageStatistics = 90,
            securityEvents = 365
        }
    end,
    
    -- Schedule automatic data cleanup
    ScheduleDataCleanup = function(self)
        -- Create cleanup timer
        self.cleanupTimer = CreateFrame("Frame")
        local elapsed = 0
        local cleanupInterval = 86400 -- 24 hours
        
        self.cleanupTimer:SetScript("OnUpdate", function(frame, dt)
            elapsed = elapsed + dt
            if elapsed >= cleanupInterval then
                elapsed = 0
                self:PerformDataCleanup()
            end
        end)
    end,
    
    -- Perform data cleanup based on retention policies
    PerformDataCleanup = function(self)
        local now = time()
        local cleaned = 0
        
        -- Clean audit logs
        if RUI.DB.profile.auditLog then
            local retention = self.retentionPolicies.auditLog * 86400 -- Convert to seconds
            local newLog = {}
            
            for _, entry in pairs(RUI.DB.profile.auditLog) do
                if entry.timestamp and (now - entry.timestamp) < retention then
                    table.insert(newLog, entry)
                else
                    cleaned = cleaned + 1
                end
            end
            
            RUI.DB.profile.auditLog = newLog
        end
        
        -- Clean performance data if not consented
        if not self.consent.performanceMetrics and RUI.DB.profile.performanceData then
            RUI.DB.profile.performanceData = nil
            cleaned = cleaned + 1
        end
        
        -- Clean analytics data if not consented
        if not self.consent.analytics and RUI.DB.profile.analyticsData then
            RUI.DB.profile.analyticsData = nil
            cleaned = cleaned + 1
        end
        
        if cleaned > 0 then
            RUI.Logger:Debug("Data cleanup: %d items removed", cleaned)
        end
    end,
    
    -- Purge non-essential data
    PurgeNonEssentialData = function(self)
        local purged = {}
        
        -- Remove analytics data
        if RUI.DB.profile.analyticsData then
            RUI.DB.profile.analyticsData = nil
            table.insert(purged, "analytics")
        end
        
        -- Remove performance metrics
        if RUI.DB.profile.performanceData then
            RUI.DB.profile.performanceData = nil
            table.insert(purged, "performance")
        end
        
        -- Remove usage statistics
        if RUI.DB.profile.usageStats then
            RUI.DB.profile.usageStats = nil
            table.insert(purged, "usage statistics")
        end
        
        -- Keep essential data (UI settings, positions, etc.)
        
        if #purged > 0 then
            RUI.Logger:Info("Purged data categories: %s", table.concat(purged, ", "))
        end
    end,
    
    -- Log data processing activity
    LogProcessingActivity = function(self, activity, dataType, purpose, legalBasis)
        if not self.consent.dataCollection then return end
        
        local entry = {
            timestamp = time(),
            activity = activity,
            dataType = dataType,
            purpose = purpose,
            legalBasis = legalBasis or "consent",
            user = UnitName("player") or "unknown"
        }
        
        table.insert(self.processingActivities, entry)
        
        -- Keep only last 100 activities
        if #self.processingActivities > 100 then
            table.remove(self.processingActivities, 1)
        end
    end,
    
    -- Check if data processing is allowed
    IsProcessingAllowed = function(self, dataType)
        if dataType == "essential" then
            return true -- Always allowed
        elseif dataType == "analytics" then
            return self.consent.analytics
        elseif dataType == "performance" then
            return self.consent.performanceMetrics
        elseif dataType == "diagnostic" then
            return self.consent.errorReporting
        elseif dataType == "usage" then
            return self.consent.usageStatistics
        end
        
        return false
    end,
    
    -- Get privacy status report
    GetPrivacyStatus = function(self)
        return {
            hasConsent = self:HasValidConsent(),
            consentDate = self.consent.consentDate,
            consentVersion = self.consent.consentVersion,
            dataCollection = self.consent.dataCollection,
            analytics = self.consent.analytics,
            errorReporting = self.consent.errorReporting,
            performanceMetrics = self.consent.performanceMetrics,
            usageStatistics = self.consent.usageStatistics,
            processingActivities = #self.processingActivities,
            lastCleanup = self.lastCleanup
        }
    end,
    
    -- Export user data (GDPR Article 20)
    ExportUserData = function(self)
        local export = {
            metadata = {
                exportDate = date("%Y-%m-%d %H:%M:%S"),
                addonVersion = "1.4.0",
                user = UnitName("player"),
                realm = GetRealmName()
            },
            consent = self.consent,
            settings = {},
            processingActivities = self.processingActivities
        }
        
        -- Export essential settings only
        if RUI.DB.profile.widgets then
            export.settings.widgets = RUI.DB.profile.widgets
        end
        
        if RUI.DB.profile.accessibility then
            export.settings.accessibility = RUI.DB.profile.accessibility
        end
        
        RUI.Logger:Info("=== Data Export (GDPR Article 20) ===")
        RUI.Logger:Info("Export Date: %s", export.metadata.exportDate)
        RUI.Logger:Info("User: %s-%s", export.metadata.user, export.metadata.realm)
        RUI.Logger:Info("Consent Status: %s", export.consent.dataCollection and "Granted" or "Withdrawn")
        RUI.Logger:Info("Settings Categories: %d", self:CountTable(export.settings))
        RUI.Logger:Info("Processing Activities: %d", #export.processingActivities)
        
        return export
    end,
    
    -- Delete all user data (GDPR Article 17)
    DeleteAllUserData = function(self)
        RUI.Logger:Warn("=== Data Deletion (GDPR Article 17) ===")
        RUI.Logger:Warn("This will delete ALL RetailUI data and reset the addon.")
        RUI.Logger:Warn("Use '/ruiprivacy confirm-delete' to proceed.")
    end,
    
    -- Confirm data deletion
    ConfirmDataDeletion = function(self)
        -- Reset all saved variables
        if RUI.DB then
            RUI.DB:ResetDB()
        end
        
        -- Clear consent
        self.consent = {
            dataCollection = false,
            analytics = false,
            errorReporting = false,
            performanceMetrics = false,
            usageStatistics = false,
            consentVersion = "1.0",
            consentDate = nil,
            lastUpdated = nil
        }
        
        -- Clear processing activities
        self.processingActivities = {}
        
        RUI.Logger:Info("All user data deleted. Addon will reload.")
        
        -- Reload UI to reset everything
        ReloadUI()
    end,
    
    -- Utility function to count table entries
    CountTable = function(self, tbl)
        local count = 0
        for _ in pairs(tbl) do
            count = count + 1
        end
        return count
    end,
}

-- Initialize privacy system
local function InitializePrivacy()
    if RUI and RUI.Privacy then
        RUI.Privacy:Initialize()
    end
end

-- Register for delayed initialization
local privacyFrame = CreateFrame("Frame")
privacyFrame:RegisterEvent("ADDON_LOADED")
privacyFrame:SetScript("OnEvent", function(self, event, addonName)
    if addonName == "RetailUI" then
        -- Initialize after core systems
        RUI.Compat.After(2.0, InitializePrivacy)
        self:UnregisterEvent("ADDON_LOADED")
    end
end)
