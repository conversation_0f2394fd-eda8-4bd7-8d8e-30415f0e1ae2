# RetailUI - WotLK 3.3.5 Compatible

A comprehensive UI addon that transforms your Classic WoW interface to look and feel like Retail WoW, specifically designed for WotLK 3.3.5 compatibility.

## 🚀 Features

- **Full WotLK 3.3.5 Compatibility** - Uses compatibility layer for modern API calls
- **Retail-style Unit Frames** - Player, Target, Focus, Pet, Target of Target, and Boss frames
- **Modern Action Bars** - Configurable action bar layouts with retail styling
- **Enhanced Minimap** - Retail-style minimap with improved functionality
- **Casting Bars** - Smooth casting bars for all units
- **Buff/Debuff Frames** - Organized buff and debuff display
- **Quest Tracking** - Enhanced quest log and tracker
- **Editor Mode** - Drag and drop UI customization
- **Grid Snapping** - Precise UI element positioning
- **Comprehensive Error Handling** - Robust error handling and logging
- **Input Validation** - Safe input processing and validation

## 📋 Installation

1. Download the RetailUI addon
2. Extract to your `Interface/AddOns/` directory
3. Restart World of Warcraft
4. Enable the addon in the AddOns menu

## 🎮 Usage

### Basic Configuration
- **Open Settings**: `/rui` or click the minimap button
- **Toggle Edit Mode**: Right-click minimap button or use settings panel
- **Reset to Defaults**: Use the reset options in settings

### Editor Mode
1. Enable Edit Mode from settings or minimap button
2. Drag UI elements to desired positions
3. Use grid snapping for precise alignment
4. Save positions automatically when exiting edit mode

### Slash Commands
- `/rui` - Open configuration panel
- `/ruitest` - Run compatibility tests (debug mode)

## 🔧 Technical Features

### WotLK 3.3.5 Compatibility Layer
- **C_Timer.After()** replacement using frame-based timers
- **UnitGUID()** compatibility wrapper
- **Modern API** fallbacks for older client versions

### Error Handling & Logging
- **Multi-level logging** (ERROR, WARN, INFO, DEBUG)
- **Input validation** for all user inputs
- **Safe operations** for module and frame management
- **Graceful degradation** when features are unavailable

### Performance Optimizations
- **Event throttling** to prevent excessive updates
- **Memory management** with proper frame cleanup
- **Efficient rendering** with optimized update cycles

## 🛠️ Development

### Architecture
- **Modular design** with separate modules for each UI component
- **Constants system** eliminates magic numbers
- **Validation layer** ensures data integrity
- **Safe operations** prevent crashes from invalid operations

### Testing
The addon includes a comprehensive testing framework:
```lua
-- Run all tests
/ruitest

-- Tests automatically run in debug mode
```

### Code Quality
- **Namespaced functions** prevent global pollution
- **Input validation** on all public APIs
- **Error handling** with detailed logging
- **Constants** instead of magic numbers

## 📁 File Structure

```
RetailUI/
├── Core.lua              # Main addon core and compatibility layer
├── Constants.lua         # Centralized constants and configuration
├── Config.lua           # Settings and configuration UI
├── Atlas.lua            # Texture atlas system
├── NineSlice.lua        # Nine-slice frame creation
├── Tests.lua            # Testing framework
├── Modules/
│   ├── ActionBar.lua    # Action bar management
│   ├── UnitFrame.lua    # Unit frame handling
│   ├── CastingBar.lua   # Casting bar implementation
│   ├── Minimap.lua      # Minimap enhancements
│   ├── BuffFrame.lua    # Buff/debuff frames
│   ├── QuestLog.lua     # Quest log improvements
│   ├── QuestTracker.lua # Quest tracking
│   └── EditorMode.lua   # UI editor functionality
├── Libs/                # Third-party libraries
└── Textures/           # UI textures and assets
```

## 🐛 Troubleshooting

### Common Issues

**Addon not loading:**
- Verify files are in correct directory
- Check TOC file interface version
- Enable in AddOns menu

**UI elements not positioning correctly:**
- Reset to defaults in settings
- Clear saved variables
- Restart game client

**Performance issues:**
- Disable debug mode
- Reduce update frequency in settings
- Check for conflicting addons

### Debug Mode
Enable debug mode in settings for:
- Detailed logging output
- Automatic test execution
- Performance monitoring
- Error diagnostics

## 📝 Changelog

### Version 1.4.0+
- ✅ Added WotLK 3.3.5 compatibility layer
- ✅ Implemented comprehensive error handling
- ✅ Added input validation system
- ✅ Created constants system
- ✅ Added testing framework
- ✅ Improved memory management
- ✅ Enhanced logging system

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests with `/ruitest`
5. Submit a pull request

## 📄 License

Licensed under the MIT License. See LICENSE file for details.

## 🙏 Acknowledgments

- Built with Ace3 libraries
- Inspired by retail WoW interface design
- Community feedback and testing
