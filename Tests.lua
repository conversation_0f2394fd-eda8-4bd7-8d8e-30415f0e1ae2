--[[
    Copyright (c) Dmitriy. All rights reserved.
    Licensed under the MIT license. See LICENSE file in the project root for details.
]]

-- Basic testing framework for RetailUI
-- This provides simple unit tests to verify functionality

RUI.Tests = {
    results = {},
    
    -- Test runner
    RunAllTests = function(self)
        self.results = {}
        RUI.Logger:Info("Starting RetailUI Tests...")
        
        -- Run all test functions
        self:TestCompatibilityLayer()
        self:TestInputValidation()
        self:TestConstants()
        self:TestSafeOperations()
        self:TestFrameCreation()
        self:TestPerformanceMonitoring()
        self:TestSecuritySystem()
        self:TestModuleArchitecture()
        self:TestIntegration()
        self:RunPerformanceBenchmarks()
        self:TestWotLKCompatibility()
        self:RunStressTests()
        self:TestEdgeCases()
        
        -- Report results
        self:ReportResults()
    end,
    
    -- Helper function to run a test
    RunTest = function(self, testName, testFunction)
        local success, error = pcall(testFunction)
        self.results[testName] = {
            success = success,
            error = error
        }
        
        if success then
            RUI.Logger:Info("✅ %s: PASSED", testName)
        else
            RUI.Logger:Error("❌ %s: FAILED - %s", testName, error or "Unknown error")
        end
    end,
    
    -- Test the compatibility layer
    TestCompatibilityLayer = function(self)
        self:RunTest("Compatibility Layer", function()
            -- Test C_Timer.After compatibility
            local called = false
            RUI.Compat.After(0.01, function()
                called = true
            end)
            
            -- Wait a bit and check
            local startTime = GetTime()
            while GetTime() - startTime < 0.1 and not called do
                -- Wait
            end
            
            assert(called, "C_Timer.After compatibility failed")
            
            -- Test UnitGUID compatibility
            local guid = RUI.Compat.UnitGUID("player")
            assert(guid ~= nil, "UnitGUID compatibility failed")
            
            -- Test version detection
            assert(type(RUI.Compat.IsWotLK()) == "boolean", "Version detection failed")
        end)
    end,
    
    -- Test input validation
    TestInputValidation = function(self)
        self:RunTest("Input Validation", function()
            -- Test scale validation
            local valid, result = RUI.Validator:ValidateScale("1.5")
            assert(valid == true and result == 1.5, "Scale validation failed for valid input")
            
            local invalid, error = RUI.Validator:ValidateScale("invalid")
            assert(invalid == false, "Scale validation should fail for invalid input")
            
            local tooLarge, error2 = RUI.Validator:ValidateScale("10")
            assert(tooLarge == false, "Scale validation should fail for too large values")
            
            -- Test frame name validation
            local validName, name = RUI.Validator:ValidateFrameName("TestFrame")
            assert(validName == true and name == "TestFrame", "Frame name validation failed")
            
            local invalidName, nameError = RUI.Validator:ValidateFrameName("")
            assert(invalidName == false, "Frame name validation should fail for empty name")
        end)
    end,
    
    -- Test constants
    TestConstants = function(self)
        self:RunTest("Constants", function()
            -- Test frame size constants
            local playerSize = RUI:GetFrameSize("PLAYER_FRAME")
            assert(playerSize.width == 192 and playerSize.height == 68, "Player frame size constant incorrect")
            
            -- Test texture coordinates
            local runeCoords = RUI:GetTextureCoords("RUNE_BLOOD")
            assert(type(runeCoords) == "table" and #runeCoords == 4, "Rune texture coordinates incorrect")
            
            -- Test color constants
            local reactionColor = RUI:GetReactionColor(5) -- Friendly
            assert(reactionColor.r == 0.0 and reactionColor.g == 1.0 and reactionColor.b == 0.0, "Reaction color incorrect")
        end)
    end,
    
    -- Test safe operations
    TestSafeOperations = function(self)
        self:RunTest("Safe Operations", function()
            -- Test safe module retrieval
            local unitFrameModule = RUI.SafeOps:GetModuleSafely("UnitFrame")
            assert(unitFrameModule ~= nil, "Safe module retrieval failed for existing module")
            
            local nonExistentModule = RUI.SafeOps:GetModuleSafely("NonExistent")
            assert(nonExistentModule == nil, "Safe module retrieval should return nil for non-existent module")
            
            -- Test frame validation
            local testFrame = CreateFrame("Frame")
            local valid, frame = RUI.Validator:ValidateFrame(testFrame)
            assert(valid == true, "Frame validation failed for valid frame")
            
            local invalid, error = RUI.Validator:ValidateFrame(nil)
            assert(invalid == false, "Frame validation should fail for nil frame")
        end)
    end,
    
    -- Test frame creation
    TestFrameCreation = function(self)
        self:RunTest("Frame Creation", function()
            -- Test creating a UI frame
            local testFrame = RUI:CreateUIFrame(100, 100, "TestFrame")
            assert(testFrame ~= nil, "Frame creation failed")
            assert(testFrame:GetWidth() == 100, "Frame width incorrect")
            assert(testFrame:GetHeight() == 100, "Frame height incorrect")
            
            -- Test invalid frame name
            local invalidFrame = RUI:CreateUIFrame(100, 100, "")
            assert(invalidFrame == nil, "Frame creation should fail for invalid name")
        end)
    end,

    -- Test performance monitoring
    TestPerformanceMonitoring = function(self)
        self:RunTest("Performance Monitoring", function()
            -- Test performance timer
            RUI.Performance:StartTimer("TestOperation")
            -- Simulate some work
            local start = GetTime()
            while GetTime() - start < 0.01 do
                -- Wait 10ms
            end
            RUI.Performance:EndTimer("TestOperation")

            -- Test report generation
            local report = RUI.Performance:GetReport()
            assert(report.TestOperation ~= nil, "Performance metrics not recorded")
            assert(report.TestOperation.callCount == 1, "Call count incorrect")

            -- Test system metrics
            local metrics = RUI.Performance:GetSystemMetrics()
            assert(metrics.frameRate > 0, "Frame rate not detected")
            assert(metrics.memoryUsage > 0, "Memory usage not detected")
        end)
    end,

    -- Test security system
    TestSecuritySystem = function(self)
        self:RunTest("Security System", function()
            -- Test input sanitization
            local sanitized = RUI.Security:SanitizeInput("test<script>", "string")
            assert(sanitized == "testscript", "Input sanitization failed")

            local sanitizedNumber = RUI.Security:SanitizeInput("123.45", "number")
            assert(sanitizedNumber == 123.45, "Number sanitization failed")

            -- Test permission system
            assert(RUI.Security:HasPermission("RetailUI", "saveSettings") == true, "Permission check failed")

            -- Test secure storage
            local stored = RUI.Security:SecureStore("testKey", "testValue", "RetailUI")
            assert(stored == true, "Secure storage failed")
        end)
    end,

    -- Test module architecture
    TestModuleArchitecture = function(self)
        self:RunTest("Module Architecture", function()
            -- Test module manager
            local report = RUI.ModuleManager:GetStatusReport()
            assert(report.totalModules > 0, "No modules registered")

            -- Test module base functionality
            local unitFrame = RUI.SafeOps:GetModuleSafely("UnitFrame")
            assert(unitFrame ~= nil, "UnitFrame module not found")
            assert(unitFrame.GetStatus ~= nil, "Module missing GetStatus method")

            local status = unitFrame:GetStatus()
            assert(status.name == "UnitFrame", "Module name incorrect")
            assert(status.initialized == true, "Module not initialized")
        end)
    end,

    -- Test integration between modules
    TestIntegration = function(self)
        self:RunTest("Module Integration", function()
            -- Test module dependencies
            local modules = {"UnitFrame", "ActionBar", "CastingBar"}

            for _, moduleName in pairs(modules) do
                local module = RUI.SafeOps:GetModuleSafely(moduleName)
                if module then
                    -- Test module can access core functionality
                    assert(module.CreateUIFrame ~= nil, "Module missing CreateUIFrame")
                    assert(module.RegisterEvent ~= nil, "Module missing RegisterEvent")

                    -- Test module status
                    local status = module:GetStatus()
                    assert(status.initialized, "Module " .. moduleName .. " not initialized")
                end
            end

            -- Test cross-module communication
            local unitFrame = RUI.SafeOps:GetModuleSafely("UnitFrame")
            local castingBar = RUI.SafeOps:GetModuleSafely("CastingBar")

            if unitFrame and castingBar then
                -- Both modules should be able to coexist
                assert(unitFrame.isEnabled, "UnitFrame not enabled")
                assert(castingBar.isEnabled, "CastingBar not enabled")
            end
        end)
    end,

    -- Performance benchmarks
    RunPerformanceBenchmarks = function(self)
        self:RunTest("Performance Benchmarks", function()
            local benchmarks = {
                {name = "Frame Creation", iterations = 100, func = function()
                    local frame = RUI:CreateUIFrame(50, 50, "BenchFrame" .. math.random(1000))
                    if frame then
                        frame:Hide()
                    end
                end},

                {name = "Event Throttling", iterations = 1000, func = function()
                    RUI.Throttle:ThrottledCall("bench_test", 0.001, function() end)
                end},

                {name = "Input Validation", iterations = 500, func = function()
                    RUI.Security:SanitizeInput("test_input_" .. math.random(100), "string")
                end},

                {name = "Memory Operations", iterations = 200, func = function()
                    local report = RUI.Memory:GetMemoryReport()
                    assert(report ~= nil, "Memory report failed")
                end}
            }

            for _, benchmark in pairs(benchmarks) do
                local startTime = GetTime()

                for i = 1, benchmark.iterations do
                    benchmark.func()
                end

                local endTime = GetTime()
                local totalTime = endTime - startTime
                local avgTime = totalTime / benchmark.iterations

                RUI.Logger:Info("Benchmark %s: %.3fms avg (%.3fs total, %d iterations)",
                    benchmark.name, avgTime * 1000, totalTime, benchmark.iterations)

                -- Performance assertions
                if avgTime > 0.01 then -- 10ms per operation is too slow
                    RUI.Logger:Warn("Performance warning: %s took %.3fms per operation",
                        benchmark.name, avgTime * 1000)
                end
            end
        end)
    end,

    -- Test WotLK 3.3.5 compatibility
    TestWotLKCompatibility = function(self)
        self:RunTest("WotLK 3.3.5 Compatibility", function()
            -- Test compatibility layer
            assert(RUI.Compat ~= nil, "Compatibility layer not found")
            assert(RUI.Compat.After ~= nil, "C_Timer.After compatibility missing")
            assert(RUI.Compat.UnitGUID ~= nil, "UnitGUID compatibility missing")

            -- Test version detection
            local isWotLK = RUI.Compat.IsWotLK()
            assert(type(isWotLK) == "boolean", "Version detection failed")

            -- Test that no modern APIs are used directly
            local modernAPIs = {"C_Timer", "C_Map", "C_QuestLog"}
            for _, api in pairs(modernAPIs) do
                if _G[api] then
                    RUI.Logger:Warn("Modern API %s detected - ensure compatibility layer is used", api)
                end
            end

            -- Test interface version
            local version = select(4, GetBuildInfo())
            if version >= 30300 and version < 40000 then
                RUI.Logger:Info("WotLK client detected: %d", version)
            else
                RUI.Logger:Warn("Non-WotLK client detected: %d", version)
            end
        end)
    end,

    -- Automated validation tests
    RunAutomatedValidation = function(self)
        self:RunTest("Automated Validation", function()
            -- Validate all modules are properly structured
            local report = RUI.ModuleManager:GetStatusReport()

            for moduleName, status in pairs(report.modules) do
                -- Check required properties
                assert(status.name ~= nil, "Module " .. moduleName .. " missing name")
                assert(status.version ~= nil, "Module " .. moduleName .. " missing version")

                -- Check module is functional
                if status.enabled then
                    local module = RUI.SafeOps:GetModuleSafely(moduleName)
                    assert(module ~= nil, "Enabled module " .. moduleName .. " not accessible")
                    assert(module.GetStatus ~= nil, "Module " .. moduleName .. " missing GetStatus")
                end
            end

            -- Validate memory management
            local memReport = RUI.Memory:GetMemoryReport()
            assert(memReport.totalFrames >= 0, "Invalid frame count")
            assert(memReport.totalTimers >= 0, "Invalid timer count")

            -- Validate security system
            assert(RUI.Security.permissions ~= nil, "Security permissions not initialized")
            assert(RUI.Security.trustedSources ~= nil, "Trusted sources not initialized")

            -- Validate performance monitoring
            local perfReport = RUI.Performance:GetReport()
            assert(type(perfReport) == "table", "Performance report invalid")
        end)
    end,

    -- Stress testing
    RunStressTests = function(self)
        self:RunTest("Stress Testing", function()
            RUI.Logger:Info("Running stress tests...")

            -- Stress test frame creation
            local frames = {}
            for i = 1, 50 do
                local frame = RUI:CreateUIFrame(10, 10, "StressFrame" .. i)
                if frame then
                    table.insert(frames, frame)
                end
            end

            assert(#frames >= 45, "Frame creation stress test failed") -- Allow some failures

            -- Cleanup stress test frames
            for _, frame in pairs(frames) do
                if frame and frame.Hide then
                    frame:Hide()
                end
            end

            -- Stress test event throttling
            local callCount = 0
            for i = 1, 1000 do
                RUI.Throttle:ThrottledCall("stress_test", 0.001, function()
                    callCount = callCount + 1
                end)
            end

            -- Should be heavily throttled
            assert(callCount < 100, "Event throttling stress test failed")

            -- Stress test memory operations
            for i = 1, 100 do
                local report = RUI.Memory:GetMemoryReport()
                assert(report ~= nil, "Memory stress test failed at iteration " .. i)
            end

            RUI.Logger:Info("Stress tests completed successfully")
        end)
    end,

    -- Edge case testing
    TestEdgeCases = function(self)
        self:RunTest("Edge Cases", function()
            -- Test invalid inputs
            local invalidInputs = {
                nil, "", "   ", "<script>", "very_long_string_" .. string.rep("x", 100),
                -1, 0, 999999, "not_a_number", {}
            }

            for _, input in pairs(invalidInputs) do
                -- Test input sanitization
                local sanitized = RUI.Security:SanitizeInput(input, "string")
                -- Should not crash, may return nil or sanitized version

                -- Test scale validation
                local valid, result = RUI.Validator:ValidateScale(input)
                -- Should handle gracefully
            end

            -- Test frame operations with invalid frames
            local invalidFrames = {nil, {}, "not_a_frame"}

            for _, frame in pairs(invalidFrames) do
                local valid, err = RUI.Validator:ValidateFrame(frame)
                assert(valid == false, "Should reject invalid frame")
            end

            -- Test module operations with non-existent modules
            local nonExistentModule = RUI.SafeOps:GetModuleSafely("NonExistentModule")
            assert(nonExistentModule == nil, "Should return nil for non-existent module")

            -- Test security with untrusted sources
            local hasPermission = RUI.Security:HasPermission("untrusted_source", "saveSettings")
            -- Should handle gracefully

            -- Test memory operations with edge cases
            RUI.Memory:CleanupModule("NonExistentModule") -- Should not crash

            -- Test performance with zero/negative values
            RUI.Performance:StartTimer("")
            RUI.Performance:EndTimer("")

            RUI.Logger:Info("Edge case tests completed")
        end)
    end,

    -- Report test results
    ReportResults = function(self)
        local passed = 0
        local failed = 0
        
        RUI.Logger:Info("=== Test Results ===")
        
        for testName, result in pairs(self.results) do
            if result.success then
                passed = passed + 1
            else
                failed = failed + 1
            end
        end
        
        RUI.Logger:Info("Tests Passed: %d", passed)
        if failed > 0 then
            RUI.Logger:Error("Tests Failed: %d", failed)
        else
            RUI.Logger:Info("All tests passed! ✅")
        end
        
        return failed == 0
    end,
}

-- Slash command to run tests
SLASH_RUITEST1 = "/ruitest"
SlashCmdList["RUITEST"] = function(msg)
    local command = string.lower(msg or "")

    if command == "all" or command == "" then
        RUI.Tests:RunAllTests()
    elseif command == "stress" then
        RUI.Tests:RunStressTests()
    elseif command == "edge" then
        RUI.Tests:TestEdgeCases()
    elseif command == "benchmark" then
        RUI.Tests:RunPerformanceBenchmarks()
    elseif command == "integration" then
        RUI.Tests:TestIntegration()
    elseif command == "validation" then
        RUI.Tests:RunAutomatedValidation()
    elseif command == "auto on" then
        RUI.AutoTest:Enable()
        RUI.Logger:Info("Automated testing enabled")
    elseif command == "auto off" then
        RUI.AutoTest:Disable()
        RUI.Logger:Info("Automated testing disabled")
    else
        RUI.Logger:Info("Test Commands:")
        RUI.Logger:Info("  /ruitest all - Run all tests (default)")
        RUI.Logger:Info("  /ruitest stress - Run stress tests")
        RUI.Logger:Info("  /ruitest edge - Run edge case tests")
        RUI.Logger:Info("  /ruitest benchmark - Run performance benchmarks")
        RUI.Logger:Info("  /ruitest integration - Run integration tests")
        RUI.Logger:Info("  /ruitest validation - Run automated validation")
        RUI.Logger:Info("  /ruitest auto on/off - Enable/disable automated testing")
    end
end

-- Slash command for security operations
SLASH_RUISECURITY1 = "/ruisecurity"
SlashCmdList["RUISECURITY"] = function(msg)
    local command = string.lower(msg or "")

    if command == "report" then
        local report = RUI.Security:GetSecurityReport()
        RUI.Logger:Info("=== Security Report ===")
        RUI.Logger:Info("Security Level: %d", report.securityLevel)
        RUI.Logger:Info("Audit Log Entries: %d", report.auditLogSize)

        if #report.recentEvents > 0 then
            RUI.Logger:Info("Recent Security Events:")
            for _, event in pairs(report.recentEvents) do
                RUI.Logger:Info("  %s: %s (%s)", event.timestamp, event.event, event.severity)
            end
        end

    elseif command == "memory" then
        RUI.Memory:PrintMemoryReport()

    elseif command == "performance" then
        RUI.Performance:PrintReport()
        RUI.Performance:PrintSystemMetrics()

    elseif command == "cleanup" then
        RUI.Memory:FullCleanup()
        RUI.Logger:Info("Manual cleanup completed")

    elseif command == "audit" then
        local report = RUI.Security:GetSecurityReport()
        if #report.recentEvents > 0 then
            RUI.Logger:Info("=== Security Audit Log ===")
            for _, event in pairs(report.recentEvents) do
                RUI.Logger:Info("%s [%s]: %s - %s", event.timestamp, event.severity, event.event, event.details)
            end
        else
            RUI.Logger:Info("No recent security events")
        end

    else
        RUI.Logger:Info("Security Commands:")
        RUI.Logger:Info("  /ruisecurity report - Show security report")
        RUI.Logger:Info("  /ruisecurity memory - Show memory usage")
        RUI.Logger:Info("  /ruisecurity performance - Show performance metrics")
        RUI.Logger:Info("  /ruisecurity cleanup - Force cleanup")
        RUI.Logger:Info("  /ruisecurity audit - Show security audit log")
    end
end

-- Automated testing system
RUI.AutoTest = {
    enabled = false,
    testInterval = 300, -- 5 minutes
    lastTestTime = 0,

    -- Enable automated testing
    Enable = function(self)
        if self.enabled then return end

        self.enabled = true
        self.lastTestTime = GetTime()

        -- Create test timer
        self.testFrame = CreateFrame("Frame")
        local elapsed = 0

        self.testFrame:SetScript("OnUpdate", function(frame, dt)
            elapsed = elapsed + dt
            if elapsed >= self.testInterval then
                elapsed = 0
                self:RunPeriodicTests()
            end
        end)

        RUI.Logger:Info("Automated testing enabled (interval: %ds)", self.testInterval)
    end,

    -- Disable automated testing
    Disable = function(self)
        if not self.enabled then return end

        self.enabled = false
        if self.testFrame then
            self.testFrame:SetScript("OnUpdate", nil)
            self.testFrame = nil
        end

        RUI.Logger:Info("Automated testing disabled")
    end,

    -- Run periodic validation tests
    RunPeriodicTests = function(self)
        RUI.Logger:Debug("Running periodic validation tests...")

        -- Quick validation tests
        local tests = {
            function() return RUI.ModuleManager:GetStatusReport().totalModules > 0 end,
            function() return RUI.Memory:GetMemoryReport().totalFrames >= 0 end,
            function() return RUI.Performance:GetSystemMetrics().frameRate > 0 end,
            function() return RUI.Security.permissions ~= nil end,
        }

        local passed = 0
        local failed = 0

        for i, test in pairs(tests) do
            local success, result = pcall(test)
            if success and result then
                passed = passed + 1
            else
                failed = failed + 1
                RUI.Logger:Warn("Periodic test %d failed: %s", i, result or "unknown error")
            end
        end

        if failed > 0 then
            RUI.Logger:Warn("Periodic validation: %d passed, %d failed", passed, failed)
            RUI.Security:AuditLog("PeriodicTestFailure", string.format("%d tests failed", failed), "WARN")
        else
            RUI.Logger:Debug("Periodic validation: all %d tests passed", passed)
        end
    end,
}

-- Auto-run tests on addon load (only in debug mode)
local function RunTestsOnLoad()
    if RUI.DB and RUI.DB.profile and RUI.DB.profile.debugMode then
        RUI.Logger:Info("Debug mode enabled, running tests...")
        RUI.Compat.After(2.0, function()
            RUI.Tests:RunAllTests()
            RUI.Tests:RunAutomatedValidation()

            -- Enable automated testing in debug mode
            RUI.AutoTest:Enable()
        end)
    end
end

-- Register for addon loaded event
local testFrame = CreateFrame("Frame")
testFrame:RegisterEvent("ADDON_LOADED")
testFrame:SetScript("OnEvent", function(self, event, addonName)
    if addonName == "RetailUI" then
        RunTestsOnLoad()
        self:UnregisterEvent("ADDON_LOADED")
    end
end)

-- Slash command for code quality
SLASH_RUIQUALITY1 = "/ruiquality"
SlashCmdList["RUIQUALITY"] = function(msg)
    local command = string.lower(msg or "")

    if command == "report" then
        RUI.CodeQuality:PrintQualityReport()

    elseif command == "validate" then
        local valid = RUI.CodeQuality:ValidateArchitecture()
        if valid then
            RUI.Logger:Info("✅ Architecture validation passed")
        else
            RUI.Logger:Warn("❌ Architecture validation failed")
        end

    elseif command == "naming" then
        local violations = RUI.CodeQuality:ValidateNamingConventions("all")
        if #violations == 0 then
            RUI.Logger:Info("✅ No naming convention violations")
        else
            RUI.Logger:Warn("Naming violations found:")
            for _, violation in pairs(violations) do
                RUI.Logger:Warn("  - %s", violation)
            end
        end

    elseif command == "complexity" then
        local analysis = RUI.CodeQuality:AnalyzeComplexity()
        RUI.Logger:Info("=== Complexity Analysis ===")
        RUI.Logger:Info("Total Modules: %d", analysis.totalModules)
        RUI.Logger:Info("Total Functions: %d", analysis.totalFunctions)
        RUI.Logger:Info("Average Complexity: %.1f functions/module", analysis.averageComplexity)

    elseif command == "dependencies" then
        local graph = RUI.CodeQuality:GetDependencyGraph()
        RUI.Logger:Info("=== Dependency Graph ===")
        RUI.Logger:Info("Nodes: %d", #graph.nodes)
        RUI.Logger:Info("Edges: %d", #graph.edges)

        for _, edge in pairs(graph.edges) do
            RUI.Logger:Info("  %s → %s", edge.from, edge.to)
        end

    elseif command == "enforce" then
        local enforced = RUI.CodeQuality:EnforceStandards()
        RUI.Logger:Info("Code standards enforcement: %d issues addressed", enforced)

    else
        RUI.Logger:Info("Code Quality Commands:")
        RUI.Logger:Info("  /ruiquality report - Generate quality report")
        RUI.Logger:Info("  /ruiquality validate - Validate architecture")
        RUI.Logger:Info("  /ruiquality naming - Check naming conventions")
        RUI.Logger:Info("  /ruiquality complexity - Analyze code complexity")
        RUI.Logger:Info("  /ruiquality dependencies - Show dependency graph")
        RUI.Logger:Info("  /ruiquality enforce - Enforce code standards")
    end
end

-- Slash command for privacy controls
SLASH_RUIPRIVACY1 = "/ruiprivacy"
SlashCmdList["RUIPRIVACY"] = function(msg)
    local command = string.lower(msg or "")

    if command == "info" then
        RUI.Privacy:ShowConsentDialog()

    elseif command == "accept" then
        RUI.Privacy:SetFullConsent()
        RUI.Logger:Info("✅ Full consent granted")

    elseif command == "minimal" then
        RUI.Privacy:SetMinimalConsent()
        RUI.Logger:Info("✅ Minimal consent set")

    elseif command == "withdraw" then
        RUI.Privacy:WithdrawConsent()
        RUI.Logger:Info("✅ Consent withdrawn")

    elseif command == "status" then
        local status = RUI.Privacy:GetPrivacyStatus()
        RUI.Logger:Info("=== Privacy Status ===")
        RUI.Logger:Info("Has Consent: %s", status.hasConsent and "Yes" or "No")
        RUI.Logger:Info("Data Collection: %s", status.dataCollection and "Enabled" or "Disabled")
        RUI.Logger:Info("Analytics: %s", status.analytics and "Enabled" or "Disabled")
        RUI.Logger:Info("Error Reporting: %s", status.errorReporting and "Enabled" or "Disabled")
        RUI.Logger:Info("Performance Metrics: %s", status.performanceMetrics and "Enabled" or "Disabled")
        RUI.Logger:Info("Usage Statistics: %s", status.usageStatistics and "Enabled" or "Disabled")

    elseif command == "export" then
        local export = RUI.Privacy:ExportUserData()
        RUI.Logger:Info("Data export completed - see chat for details")

    elseif command == "delete" then
        RUI.Privacy:DeleteAllUserData()

    elseif command == "confirm-delete" then
        RUI.Privacy:ConfirmDataDeletion()

    elseif command == "custom" then
        RUI.Logger:Info("=== Custom Privacy Settings ===")
        RUI.Logger:Info("Use these commands to customize your privacy preferences:")
        RUI.Logger:Info("  /ruiprivacy analytics on/off - Toggle analytics")
        RUI.Logger:Info("  /ruiprivacy errors on/off - Toggle error reporting")
        RUI.Logger:Info("  /ruiprivacy performance on/off - Toggle performance metrics")
        RUI.Logger:Info("  /ruiprivacy usage on/off - Toggle usage statistics")

    elseif string.match(command, "^analytics ") then
        local setting = string.match(command, "analytics (.+)")
        if setting == "on" then
            RUI.Privacy.consent.analytics = true
            RUI.Privacy:SaveConsent()
            RUI.Logger:Info("Analytics enabled")
        elseif setting == "off" then
            RUI.Privacy.consent.analytics = false
            RUI.Privacy:SaveConsent()
            RUI.Logger:Info("Analytics disabled")
        end

    elseif string.match(command, "^errors ") then
        local setting = string.match(command, "errors (.+)")
        if setting == "on" then
            RUI.Privacy.consent.errorReporting = true
            RUI.Privacy:SaveConsent()
            RUI.Logger:Info("Error reporting enabled")
        elseif setting == "off" then
            RUI.Privacy.consent.errorReporting = false
            RUI.Privacy:SaveConsent()
            RUI.Logger:Info("Error reporting disabled")
        end

    else
        RUI.Logger:Info("Privacy Commands:")
        RUI.Logger:Info("  /ruiprivacy info - Show privacy information")
        RUI.Logger:Info("  /ruiprivacy accept - Accept all data processing")
        RUI.Logger:Info("  /ruiprivacy minimal - Accept only essential data")
        RUI.Logger:Info("  /ruiprivacy withdraw - Withdraw consent")
        RUI.Logger:Info("  /ruiprivacy status - Show current privacy status")
        RUI.Logger:Info("  /ruiprivacy custom - Customize privacy settings")
        RUI.Logger:Info("  /ruiprivacy export - Export your data (GDPR)")
        RUI.Logger:Info("  /ruiprivacy delete - Delete all data (GDPR)")
    end
end
