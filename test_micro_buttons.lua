--[[
    Test file to demonstrate the new dynamic micro button support
    
    This file shows how the RetailUI ActionBar module now automatically
    discovers and supports arbitrary micro buttons.
--]]

-- Example of how to test the new functionality
local function TestMicroButtonDiscovery()
    print("=== Testing Dynamic Micro Button Discovery ===")
    
    -- Simulate what the DiscoverMicroButtons function does
    local knownButtonOrder = {
        'CharacterMicroButton',
        'SpellbookMicroButton', 
        'TalentMicroButton',
        'AchievementMicroButton',
        'QuestLogMicroButton',
        'SocialsMicroButton',
        'PVPMicroButton',
        'LFDMicroButton',
        'MainMenuMicroButton',
        'HelpMicroButton'
    }
    
    local discoveredButtons = {}
    local buttonSet = {}
    
    print("Checking for known buttons:")
    for _, buttonName in ipairs(knownButtonOrder) do
        local button = _G[buttonName]
        if button and button.GetObjectType and button:GetObjectType() == "Button" then
            table.insert(discoveredButtons, button)
            buttonSet[buttonName] = true
            print("  ✓ Found: " .. buttonName)
        else
            print("  ✗ Missing: " .. buttonName)
        end
    end
    
    print("\nScanning for additional micro buttons:")
    local customButtons = {}
    for name, obj in pairs(_G) do
        if type(name) == "string" and name:match("MicroButton$") and 
           obj and type(obj) == "table" and obj.GetObjectType and 
           obj:GetObjectType() == "Button" and not buttonSet[name] then
            table.insert(customButtons, name)
            table.insert(discoveredButtons, obj)
            buttonSet[name] = true
        end
    end
    
    if #customButtons > 0 then
        print("  Custom buttons found:")
        for _, buttonName in ipairs(customButtons) do
            print("    ✓ " .. buttonName)
        end
    else
        print("  No custom buttons found")
    end
    
    print("\nTotal buttons discovered: " .. #discoveredButtons)
    print("This is what RetailUI will now support automatically!")
    
    return discoveredButtons
end

-- Example of how to create a custom micro button (for testing)
local function CreateTestMicroButton()
    if _G["TestCustomMicroButton"] then
        return _G["TestCustomMicroButton"]
    end
    
    local button = CreateFrame("Button", "TestCustomMicroButton", UIParent)
    button:SetSize(21, 29)
    button:SetPoint("CENTER", UIParent, "CENTER", 0, 0)
    
    -- Add some basic textures
    local normalTexture = button:CreateTexture(nil, "BACKGROUND")
    normalTexture:SetAllPoints(button)
    normalTexture:SetColorTexture(0.2, 0.2, 0.8, 1)
    button:SetNormalTexture(normalTexture)
    
    local highlightTexture = button:CreateTexture(nil, "HIGHLIGHT")
    highlightTexture:SetAllPoints(button)
    highlightTexture:SetColorTexture(0.3, 0.3, 1, 0.5)
    button:SetHighlightTexture(highlightTexture)
    
    button:SetScript("OnClick", function()
        print("Test custom micro button clicked!")
    end)
    
    print("Created TestCustomMicroButton for testing")
    return button
end

-- Example usage:
print("=== RetailUI Dynamic Micro Button Test ===")
print("This demonstrates the new arbitrary button support.")
print("")

-- Test current state
TestMicroButtonDiscovery()

print("")
print("=== Creating Test Button ===")
CreateTestMicroButton()

print("")
print("=== Testing Again With Custom Button ===")
TestMicroButtonDiscovery()

print("")
print("=== How to Use ===")
print("1. The RetailUI ActionBar module now automatically discovers all micro buttons")
print("2. It maintains the standard order for known buttons")
print("3. Custom buttons (like Ascension's) are automatically added at the end")
print("4. Call Module:RefreshMicroButtons() to refresh if buttons are added dynamically")
print("5. The micro bar frame automatically resizes to fit all buttons")
print("")
print("This means RetailUI will 'just work' with any server's custom micro buttons!")
